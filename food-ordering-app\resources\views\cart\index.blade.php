@extends('layouts.app')

@section('title', 'Shopping Cart - Food Ordering App')

@section('content')
<div class="container-mobile">
    <div class="py-6">
        <h1 class="text-2xl font-bold mb-6">Shopping Cart</h1>
        
        @if($cartItems->count() > 0)
            <!-- Cart Items -->
            <div class="space-y-4 mb-6">
                @foreach($cartItems as $cartItem)
                    @php
                        $item = $cartItem->foodItem ?? $cartItem->package;
                        $itemType = $cartItem->foodItem ? 'food_item' : 'package';
                    @endphp
                    <div class="bg-white rounded-lg shadow-sm p-4" id="cart-item-{{ $cartItem->id }}">
                        <div class="flex items-start space-x-4">
                            <!-- Item Image -->
                            <div class="flex-shrink-0">
                                @if($item->image)
                                    <img src="{{ $item->image }}" alt="{{ $item->name }}" class="w-16 h-16 rounded-lg object-cover">
                                @else
                                    <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-image text-gray-400"></i>
                                    </div>
                                @endif
                            </div>
                            
                            <!-- Item Details -->
                            <div class="flex-1 min-w-0">
                                <h3 class="font-semibold text-lg">{{ $item->name }}</h3>
                                <p class="text-sm text-gray-600 mb-2">
                                    @if($cartItem->foodItem)
                                        {{ $cartItem->foodItem->category->name }} • {{ $cartItem->foodItem->cuisine->name }}
                                    @else
                                        {{ $cartItem->package->package_type }} Package
                                        @if($cartItem->package->serves_people)
                                            • Serves {{ $cartItem->package->serves_people }}
                                        @endif
                                    @endif
                                </p>
                                
                                <!-- Order Type and Pricing -->
                                <div class="flex items-center justify-between mb-3">
                                    <div>
                                        @if($cartItem->order_type === 'bulk')
                                            <span class="text-sm text-orange-600 font-medium">Bulk Order</span>
                                            <p class="text-sm text-gray-600">{{ $cartItem->unit_weight }}kg × ${{ number_format($cartItem->unit_price, 2) }}/kg</p>
                                        @else
                                            <span class="text-sm text-orange-600 font-medium">Unit Order</span>
                                            <p class="text-sm text-gray-600">{{ $cartItem->quantity }} × ${{ number_format($cartItem->unit_price, 2) }}</p>
                                        @endif
                                    </div>
                                    <div class="text-right">
                                        <p class="text-lg font-bold text-orange-600">${{ number_format($cartItem->total_price, 2) }}</p>
                                    </div>
                                </div>
                                
                                <!-- Quantity Controls -->
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        @if($cartItem->order_type === 'bulk')
                                            <!-- Weight Input for Bulk Orders -->
                                            <div class="flex items-center space-x-2">
                                                <label class="text-sm text-gray-600">Weight (kg):</label>
                                                <input type="number" 
                                                       value="{{ $cartItem->unit_weight }}" 
                                                       min="0.1" 
                                                       step="0.1" 
                                                       class="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
                                                       onchange="updateCartItem({{ $cartItem->id }}, 1, this.value)">
                                            </div>
                                        @else
                                            <!-- Quantity Controls for Unit Orders -->
                                            <div class="flex items-center space-x-2">
                                                <button onclick="updateCartItem({{ $cartItem->id }}, {{ $cartItem->quantity - 1 }})" 
                                                        class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors"
                                                        {{ $cartItem->quantity <= 1 ? 'disabled' : '' }}>
                                                    <i class="fas fa-minus text-sm"></i>
                                                </button>
                                                <span class="w-8 text-center font-medium">{{ $cartItem->quantity }}</span>
                                                <button onclick="updateCartItem({{ $cartItem->id }}, {{ $cartItem->quantity + 1 }})" 
                                                        class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors">
                                                    <i class="fas fa-plus text-sm"></i>
                                                </button>
                                            </div>
                                        @endif
                                    </div>
                                    
                                    <!-- Remove Button -->
                                    <button onclick="removeCartItem({{ $cartItem->id }})" 
                                            class="text-red-600 hover:text-red-800 transition-colors">
                                        <i class="fas fa-trash text-sm"></i>
                                    </button>
                                </div>
                                
                                <!-- Special Instructions -->
                                @if($cartItem->special_instructions)
                                    <div class="mt-2 p-2 bg-gray-50 rounded text-sm">
                                        <strong>Special Instructions:</strong> {{ $cartItem->special_instructions }}
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            
            <!-- Cart Summary -->
            <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
                <h3 class="font-semibold text-lg mb-4">Order Summary</h3>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span>Subtotal ({{ $cartCount }} items)</span>
                        <span>${{ number_format($cartTotal, 2) }}</span>
                    </div>
                    <div class="flex justify-between text-sm text-gray-600">
                        <span>Tax (10%)</span>
                        <span>${{ number_format($cartTotal * 0.10, 2) }}</span>
                    </div>
                    <div class="flex justify-between text-sm text-gray-600">
                        <span>Delivery Fee</span>
                        <span>
                            @if($cartTotal >= 50)
                                <span class="text-green-600">FREE</span>
                            @else
                                $5.99
                            @endif
                        </span>
                    </div>
                    <hr class="my-2">
                    <div class="flex justify-between font-bold text-lg">
                        <span>Total</span>
                        <span class="text-orange-600">
                            ${{ number_format($cartTotal + ($cartTotal * 0.10) + ($cartTotal >= 50 ? 0 : 5.99), 2) }}
                        </span>
                    </div>
                </div>
                
                @if($cartTotal < 50)
                    <div class="mt-3 p-3 bg-blue-50 rounded-lg">
                        <p class="text-sm text-blue-800">
                            <i class="fas fa-info-circle mr-1"></i>
                            Add ${{ number_format(50 - $cartTotal, 2) }} more for free delivery!
                        </p>
                    </div>
                @endif
            </div>
            
            <!-- Action Buttons -->
            <div class="space-y-3">
                <a href="{{ route('orders.checkout') }}" 
                   class="w-full bg-orange-600 text-white py-3 rounded-lg font-semibold text-center block hover:bg-orange-700 transition-colors">
                    Proceed to Checkout
                </a>
                
                <div class="flex space-x-3">
                    <a href="{{ route('menu.index') }}" 
                       class="flex-1 bg-gray-200 text-gray-800 py-2 rounded-lg font-medium text-center hover:bg-gray-300 transition-colors">
                        Continue Shopping
                    </a>
                    
                    <button onclick="clearCart()" 
                            class="flex-1 bg-red-100 text-red-800 py-2 rounded-lg font-medium hover:bg-red-200 transition-colors">
                        Clear Cart
                    </button>
                </div>
            </div>
            
        @else
            <!-- Empty Cart -->
            <div class="text-center py-12">
                <i class="fas fa-shopping-cart text-gray-400 text-6xl mb-4"></i>
                <h2 class="text-xl font-semibold text-gray-600 mb-2">Your cart is empty</h2>
                <p class="text-gray-500 mb-6">Add some delicious items to get started!</p>
                <a href="{{ route('menu.index') }}" 
                   class="bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-orange-700 transition-colors">
                    Browse Menu
                </a>
            </div>
        @endif
    </div>
</div>

@push('scripts')
<script>
    // Update cart item quantity
    function updateCartItem(cartItemId, quantity, weight = null) {
        if (quantity < 1) return;
        
        const data = { quantity: quantity };
        if (weight !== null) {
            data.unit_weight = weight;
        }
        
        $.ajax({
            url: `/cart/update/${cartItemId}`,
            method: 'PATCH',
            data: data,
            success: function(response) {
                if (response.success) {
                    // Update the item total in the UI
                    $(`#cart-item-${cartItemId}`).find('.text-lg.font-bold.text-orange-600').text('$' + response.item_total);
                    
                    // Reload the page to update totals
                    location.reload();
                }
            },
            error: function(xhr) {
                const error = xhr.responseJSON?.error || 'Error updating cart item';
                showToast(error, 'error');
            }
        });
    }
    
    // Remove cart item
    function removeCartItem(cartItemId) {
        if (!confirm('Are you sure you want to remove this item from your cart?')) {
            return;
        }
        
        $.ajax({
            url: `/cart/remove/${cartItemId}`,
            method: 'DELETE',
            success: function(response) {
                if (response.success) {
                    showToast(response.message);
                    $(`#cart-item-${cartItemId}`).fadeOut(300, function() {
                        $(this).remove();
                        
                        // Check if cart is empty
                        if (response.cart_count === 0) {
                            location.reload();
                        }
                    });
                    updateCartCount();
                }
            },
            error: function(xhr) {
                const error = xhr.responseJSON?.error || 'Error removing item from cart';
                showToast(error, 'error');
            }
        });
    }
    
    // Clear entire cart
    function clearCart() {
        if (!confirm('Are you sure you want to clear your entire cart?')) {
            return;
        }
        
        $.ajax({
            url: '/cart/clear',
            method: 'DELETE',
            success: function(response) {
                if (response.success) {
                    showToast(response.message);
                    location.reload();
                }
            },
            error: function(xhr) {
                const error = xhr.responseJSON?.error || 'Error clearing cart';
                showToast(error, 'error');
            }
        });
    }
</script>
@endpush
@endsection
