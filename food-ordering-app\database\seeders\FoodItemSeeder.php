<?php

namespace Database\Seeders;

use App\Models\FoodItem;
use App\Models\Category;
use App\Models\Cuisine;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class FoodItemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = Category::all();
        $cuisines = Cuisine::all();

        $foodItems = [
            // Indian Main Course
            [
                'name' => 'Butter Chicken',
                'slug' => 'butter-chicken',
                'description' => 'Creamy tomato-based curry with tender chicken pieces',
                'ingredients' => 'Chicken, tomatoes, cream, butter, spices',
                'price_per_unit' => 15.99,
                'price_per_kg' => 45.00,
                'category' => 'Main Course',
                'cuisine' => 'Indian',
                'is_vegetarian' => false,
                'is_spicy' => true,
                'spice_level' => 'medium',
                'is_popular' => true,
                'is_featured' => true,
                'allow_bulk_order' => true,
                'calories_per_unit' => 450,
                'preparation_time' => 25,
            ],
            [
                'name' => 'Paneer Tikka Masala',
                'slug' => 'paneer-tikka-masala',
                'description' => 'Grilled cottage cheese in rich tomato gravy',
                'ingredients' => 'Paneer, tomatoes, cream, onions, spices',
                'price_per_unit' => 13.99,
                'price_per_kg' => 40.00,
                'category' => 'Main Course',
                'cuisine' => 'Indian',
                'is_vegetarian' => true,
                'is_spicy' => true,
                'spice_level' => 'medium',
                'is_popular' => true,
                'allow_bulk_order' => true,
                'calories_per_unit' => 380,
                'preparation_time' => 20,
            ],
            
            // Chinese Main Course
            [
                'name' => 'Sweet and Sour Chicken',
                'slug' => 'sweet-and-sour-chicken',
                'description' => 'Crispy chicken with bell peppers in tangy sauce',
                'ingredients' => 'Chicken, bell peppers, pineapple, sweet and sour sauce',
                'price_per_unit' => 14.99,
                'category' => 'Main Course',
                'cuisine' => 'Chinese',
                'is_vegetarian' => false,
                'is_popular' => true,
                'calories_per_unit' => 420,
                'preparation_time' => 18,
            ],
            [
                'name' => 'Vegetable Fried Rice',
                'slug' => 'vegetable-fried-rice',
                'description' => 'Wok-fried rice with mixed vegetables and soy sauce',
                'ingredients' => 'Rice, mixed vegetables, soy sauce, garlic',
                'price_per_unit' => 11.99,
                'price_per_kg' => 25.00,
                'category' => 'Rice Dishes',
                'cuisine' => 'Chinese',
                'is_vegetarian' => true,
                'is_popular' => true,
                'allow_bulk_order' => true,
                'calories_per_unit' => 320,
                'preparation_time' => 15,
            ],
            
            // Italian
            [
                'name' => 'Margherita Pizza',
                'slug' => 'margherita-pizza',
                'description' => 'Classic pizza with tomato sauce, mozzarella, and basil',
                'ingredients' => 'Pizza dough, tomato sauce, mozzarella, basil',
                'price_per_unit' => 16.99,
                'category' => 'Pizza',
                'cuisine' => 'Italian',
                'is_vegetarian' => true,
                'is_popular' => true,
                'is_featured' => true,
                'calories_per_unit' => 550,
                'preparation_time' => 20,
            ],
            [
                'name' => 'Spaghetti Carbonara',
                'slug' => 'spaghetti-carbonara',
                'description' => 'Creamy pasta with bacon, eggs, and parmesan cheese',
                'ingredients' => 'Spaghetti, bacon, eggs, parmesan, black pepper',
                'price_per_unit' => 17.99,
                'category' => 'Pasta',
                'cuisine' => 'Italian',
                'is_vegetarian' => false,
                'calories_per_unit' => 620,
                'preparation_time' => 15,
            ],
            
            // American
            [
                'name' => 'Classic Cheeseburger',
                'slug' => 'classic-cheeseburger',
                'description' => 'Juicy beef patty with cheese, lettuce, tomato, and pickles',
                'ingredients' => 'Beef patty, cheese, lettuce, tomato, pickles, bun',
                'price_per_unit' => 12.99,
                'category' => 'Burgers',
                'cuisine' => 'American',
                'is_vegetarian' => false,
                'is_popular' => true,
                'calories_per_unit' => 580,
                'preparation_time' => 12,
            ],
            
            // Appetizers
            [
                'name' => 'Chicken Wings',
                'slug' => 'chicken-wings',
                'description' => 'Crispy chicken wings with your choice of sauce',
                'ingredients' => 'Chicken wings, buffalo sauce, celery, blue cheese',
                'price_per_unit' => 9.99,
                'price_per_kg' => 35.00,
                'category' => 'Appetizers',
                'cuisine' => 'American',
                'is_vegetarian' => false,
                'is_spicy' => true,
                'spice_level' => 'hot',
                'is_popular' => true,
                'allow_bulk_order' => true,
                'calories_per_unit' => 280,
                'preparation_time' => 15,
            ],
            [
                'name' => 'Vegetable Spring Rolls',
                'slug' => 'vegetable-spring-rolls',
                'description' => 'Crispy rolls filled with fresh vegetables',
                'ingredients' => 'Cabbage, carrots, bean sprouts, spring roll wrapper',
                'price_per_unit' => 7.99,
                'category' => 'Appetizers',
                'cuisine' => 'Chinese',
                'is_vegetarian' => true,
                'is_vegan' => true,
                'calories_per_unit' => 180,
                'preparation_time' => 10,
            ],
            
            // Desserts
            [
                'name' => 'Chocolate Brownie',
                'slug' => 'chocolate-brownie',
                'description' => 'Rich and fudgy chocolate brownie with vanilla ice cream',
                'ingredients' => 'Chocolate, butter, eggs, flour, vanilla ice cream',
                'price_per_unit' => 6.99,
                'category' => 'Desserts',
                'cuisine' => 'American',
                'is_vegetarian' => true,
                'calories_per_unit' => 420,
                'preparation_time' => 5,
            ],
            [
                'name' => 'Tiramisu',
                'slug' => 'tiramisu',
                'description' => 'Classic Italian dessert with coffee and mascarpone',
                'ingredients' => 'Ladyfingers, coffee, mascarpone, cocoa powder',
                'price_per_unit' => 8.99,
                'category' => 'Desserts',
                'cuisine' => 'Italian',
                'is_vegetarian' => true,
                'is_featured' => true,
                'calories_per_unit' => 350,
                'preparation_time' => 5,
            ],
            
            // Beverages
            [
                'name' => 'Fresh Orange Juice',
                'slug' => 'fresh-orange-juice',
                'description' => 'Freshly squeezed orange juice',
                'ingredients' => 'Fresh oranges',
                'price_per_unit' => 4.99,
                'category' => 'Beverages',
                'cuisine' => 'American',
                'is_vegetarian' => true,
                'is_vegan' => true,
                'calories_per_unit' => 110,
                'preparation_time' => 3,
            ],
            [
                'name' => 'Mango Lassi',
                'slug' => 'mango-lassi',
                'description' => 'Traditional Indian yogurt drink with mango',
                'ingredients' => 'Yogurt, mango, sugar, cardamom',
                'price_per_unit' => 5.99,
                'category' => 'Beverages',
                'cuisine' => 'Indian',
                'is_vegetarian' => true,
                'calories_per_unit' => 180,
                'preparation_time' => 5,
            ],
        ];

        foreach ($foodItems as $itemData) {
            $category = $categories->where('name', $itemData['category'])->first();
            $cuisine = $cuisines->where('name', $itemData['cuisine'])->first();
            
            if ($category && $cuisine) {
                FoodItem::create([
                    'name' => $itemData['name'],
                    'slug' => $itemData['slug'],
                    'description' => $itemData['description'],
                    'ingredients' => $itemData['ingredients'],
                    'price_per_unit' => $itemData['price_per_unit'],
                    'price_per_kg' => $itemData['price_per_kg'] ?? null,
                    'category_id' => $category->id,
                    'cuisine_id' => $cuisine->id,
                    'is_vegetarian' => $itemData['is_vegetarian'] ?? false,
                    'is_vegan' => $itemData['is_vegan'] ?? false,
                    'is_spicy' => $itemData['is_spicy'] ?? false,
                    'spice_level' => $itemData['spice_level'] ?? null,
                    'is_popular' => $itemData['is_popular'] ?? false,
                    'is_featured' => $itemData['is_featured'] ?? false,
                    'allow_bulk_order' => $itemData['allow_bulk_order'] ?? false,
                    'calories_per_unit' => $itemData['calories_per_unit'] ?? null,
                    'preparation_time' => $itemData['preparation_time'] ?? null,
                    'is_available' => true,
                ]);
            }
        }
    }
}
