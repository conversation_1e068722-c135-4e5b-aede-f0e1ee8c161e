<?php $__env->startSection('title', 'Menu - Food Ordering App'); ?>

<?php $__env->startSection('content'); ?>
<!-- Quick Filters Slider (below nav bar) -->
<div class="bg-white border-b border-gray-200 sticky top-16 z-40">
    <div class="container-mobile">
        <div class="py-3">
            <div class="flex space-x-2 overflow-x-auto pb-2">
                <a href="<?php echo e(route('menu.index')); ?>" class="filter-btn bg-orange-100 text-orange-800 px-4 py-2 rounded-full whitespace-nowrap hover:bg-orange-200 transition-colors">
                    <i class="fas fa-utensils mr-1"></i> Main Course
                </a>
                <a href="<?php echo e(route('menu.index')); ?>" class="filter-btn bg-blue-100 text-blue-800 px-4 py-2 rounded-full whitespace-nowrap hover:bg-blue-200 transition-colors">
                    <i class="fas fa-globe mr-1"></i> All Cuisines
                </a>
                <a href="<?php echo e(route('search', ['vegetarian' => 1])); ?>" class="filter-btn bg-green-100 text-green-800 px-4 py-2 rounded-full whitespace-nowrap hover:bg-green-200 transition-colors">
                    <i class="fas fa-leaf mr-1"></i> Vegetarian
                </a>
                <a href="<?php echo e(route('search', ['popular' => 1])); ?>" class="filter-btn bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full whitespace-nowrap hover:bg-yellow-200 transition-colors">
                    <i class="fas fa-star mr-1"></i> Popular
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container-mobile">
    <!-- Page Header -->
    <div class="py-6">
        <h1 class="text-2xl font-bold mb-4">Our Menu</h1>

        <!-- First Row Filters -->
        <div class="mb-4">
            <div class="flex flex-wrap gap-2">
                <!-- Vegetarian Filter -->
                <a href="<?php echo e(route('menu.index', array_merge(request()->query(), ['vegetarian' => request('vegetarian') ? null : 1]))); ?>"
                   class="filter-btn px-4 py-2 rounded-full text-sm font-medium border <?php echo e(request('vegetarian') ? 'active' : 'bg-white text-gray-700 border-gray-300'); ?>">
                    <i class="fas fa-leaf mr-1"></i>
                    Vegetarian
                </a>

                <!-- Popular Filter -->
                <a href="<?php echo e(route('menu.index', array_merge(request()->query(), ['popular' => request('popular') ? null : 1]))); ?>"
                   class="filter-btn px-4 py-2 rounded-full text-sm font-medium border <?php echo e(request('popular') ? 'active' : 'bg-white text-gray-700 border-gray-300'); ?>">
                    <i class="fas fa-star mr-1"></i>
                    Popular
                </a>

                <!-- Order Type Filter -->
                <select onchange="window.location.href = updateUrlParameter('order_type', this.value)"
                        class="filter-btn px-4 py-2 rounded-full text-sm font-medium border bg-white text-gray-700 border-gray-300">
                    <option value="all" <?php echo e(request('order_type') == 'all' ? 'selected' : ''); ?>>All Items</option>
                    <option value="unit" <?php echo e(request('order_type') == 'unit' ? 'selected' : ''); ?>>Unit Order</option>
                    <option value="bulk" <?php echo e(request('order_type') == 'bulk' ? 'selected' : ''); ?>>Bulk Order</option>
                </select>
            </div>
        </div>

        <!-- Second Row Filters -->
        <div class="mb-6">
            <div class="flex flex-wrap gap-2 mb-3">
                <!-- Category Filter -->
                <select onchange="window.location.href = updateUrlParameter('category', this.value)"
                        class="filter-btn px-4 py-2 rounded-full text-sm font-medium border bg-white text-gray-700 border-gray-300">
                    <option value="">All Categories</option>
                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($category->id); ?>" <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                            <?php echo e($category->name); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>

                <!-- Cuisine Filter -->
                <select onchange="window.location.href = updateUrlParameter('cuisine', this.value)"
                        class="filter-btn px-4 py-2 rounded-full text-sm font-medium border bg-white text-gray-700 border-gray-300">
                    <option value="">All Cuisines</option>
                    <?php $__currentLoopData = $cuisines; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cuisine): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($cuisine->id); ?>" <?php echo e(request('cuisine') == $cuisine->id ? 'selected' : ''); ?>>
                            <?php echo e($cuisine->name); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>

                <!-- Sort Filter -->
                <select onchange="window.location.href = updateUrlParameter('sort', this.value)"
                        class="filter-btn px-4 py-2 rounded-full text-sm font-medium border bg-white text-gray-700 border-gray-300">
                    <option value="name" <?php echo e(request('sort') == 'name' ? 'selected' : ''); ?>>Sort by Name</option>
                    <option value="price" <?php echo e(request('sort') == 'price' ? 'selected' : ''); ?>>Sort by Price</option>
                    <option value="popular" <?php echo e(request('sort') == 'popular' ? 'selected' : ''); ?>>Sort by Popularity</option>
                </select>
            </div>

            <!-- Clear Filters -->
            <?php if(request()->hasAny(['category', 'cuisine', 'vegetarian', 'popular', 'order_type', 'sort'])): ?>
                <a href="<?php echo e(route('menu.index')); ?>" class="text-orange-600 text-sm font-medium">
                    <i class="fas fa-times mr-1"></i>Clear All Filters
                </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Menu Tabs -->
    <div class="mb-6">
        <div class="flex border-b border-gray-200">
            <button onclick="showTab('packages')" id="packages-tab" class="tab-btn px-4 py-2 font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300">
                Packages
            </button>
            <button onclick="showTab('food-items')" id="food-items-tab" class="tab-btn px-4 py-2 font-medium text-orange-600 border-b-2 border-orange-600">
                Food Items
            </button>
        </div>
    </div>

    <!-- Packages Section -->
    <div id="packages-section" class="hidden">
        <?php if($packages->count() > 0): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <?php $__currentLoopData = $packages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="food-card bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="relative">
                        <?php if($package->image): ?>
                            <img src="<?php echo e($package->image); ?>" alt="<?php echo e($package->name); ?>" class="w-full h-48 object-cover">
                        <?php else: ?>
                            <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-image text-gray-400 text-3xl"></i>
                            </div>
                        <?php endif; ?>
                        <?php if($package->is_popular): ?>
                            <span class="absolute top-2 left-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-semibold">
                                <i class="fas fa-star mr-1"></i>Popular
                            </span>
                        <?php endif; ?>
                        <?php if($package->discount_percentage > 0): ?>
                            <span class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-semibold">
                                <?php echo e($package->discount_percentage); ?>% OFF
                            </span>
                        <?php endif; ?>
                    </div>
                    <div class="p-4">
                        <div class="flex items-start justify-between mb-2">
                            <h3 class="font-semibold text-lg"><?php echo e($package->name); ?></h3>
                            <div class="flex space-x-1">
                                <?php if($package->is_vegetarian): ?>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                                        <i class="fas fa-leaf"></i>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <p class="text-gray-600 text-sm mb-3"><?php echo e($package->description); ?></p>

                        <!-- Package Items Preview -->
                        <?php if($package->foodItems->count() > 0): ?>
                            <div class="mb-3">
                                <p class="text-xs text-gray-500 mb-1">Includes:</p>
                                <div class="text-xs text-gray-600">
                                    <?php $__currentLoopData = $package->foodItems->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span><?php echo e($item->name); ?><?php echo e(!$loop->last ? ', ' : ''); ?></span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($package->foodItems->count() > 3): ?>
                                        <span class="text-orange-600">+<?php echo e($package->foodItems->count() - 3); ?> more</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="flex items-center justify-between">
                            <div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-xl font-bold text-orange-600">$<?php echo e(number_format($package->price, 2)); ?></span>
                                    <?php if($package->original_price && $package->original_price > $package->price): ?>
                                        <span class="text-sm text-gray-500 line-through">$<?php echo e(number_format($package->original_price, 2)); ?></span>
                                    <?php endif; ?>
                                </div>
                                <?php if($package->serves_people): ?>
                                    <p class="text-xs text-gray-500">Serves <?php echo e($package->serves_people); ?> people</p>
                                <?php endif; ?>
                            </div>
                            <div class="flex space-x-2">
                                <a href="<?php echo e(route('menu.package', $package->slug)); ?>" class="bg-gray-100 text-gray-700 px-3 py-2 rounded text-sm hover:bg-gray-200 transition-colors">
                                    View Details
                                </a>
                                <button onclick="addToCart('package', <?php echo e($package->id); ?>)" class="bg-orange-600 text-white px-4 py-2 rounded text-sm hover:bg-orange-700 transition-colors">
                                    Add to Cart
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Packages Pagination -->
            <div class="mb-8">
                <?php echo e($packages->appends(request()->query())->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-12">
                <i class="fas fa-box-open text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-600">No packages found matching your criteria.</p>
            </div>
        <?php endif; ?>
    </div>

    <!-- Food Items Section -->
    <div id="food-items-section">
        <?php if($foodItems->count() > 0): ?>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
                <?php $__currentLoopData = $foodItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="food-card bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="relative">
                        <?php if($item->image): ?>
                            <img src="<?php echo e($item->image); ?>" alt="<?php echo e($item->name); ?>" class="w-full h-32 object-cover">
                        <?php else: ?>
                            <div class="w-full h-32 bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-image text-gray-400"></i>
                            </div>
                        <?php endif; ?>
                        <div class="absolute top-1 left-1 flex space-x-1">
                            <?php if($item->is_vegetarian): ?>
                                <span class="bg-green-500 text-white px-1 py-0.5 rounded text-xs">
                                    <i class="fas fa-leaf"></i>
                                </span>
                            <?php endif; ?>
                            <?php if($item->is_popular): ?>
                                <span class="bg-yellow-500 text-white px-1 py-0.5 rounded text-xs">
                                    <i class="fas fa-star"></i>
                                </span>
                            <?php endif; ?>
                        </div>
                        <?php if($item->is_spicy): ?>
                            <span class="absolute top-1 right-1 bg-red-500 text-white px-1 py-0.5 rounded text-xs">
                                <i class="fas fa-pepper-hot"></i>
                            </span>
                        <?php endif; ?>
                    </div>
                    <div class="p-3">
                        <h3 class="font-semibold text-sm mb-1"><?php echo e($item->name); ?></h3>
                        <p class="text-xs text-gray-600 mb-2"><?php echo e($item->category->name); ?> • <?php echo e($item->cuisine->name); ?></p>
                        <div class="flex items-center justify-between">
                            <div>
                                <span class="text-sm font-bold text-orange-600">$<?php echo e(number_format($item->price_per_unit, 2)); ?></span>
                                <?php if($item->allow_bulk_order && $item->price_per_kg): ?>
                                    <span class="text-xs text-gray-500 block">$<?php echo e(number_format($item->price_per_kg, 2)); ?>/kg</span>
                                <?php endif; ?>
                            </div>
                            <div class="flex space-x-1">
                                <a href="<?php echo e(route('menu.food-item', $item->slug)); ?>" class="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs hover:bg-gray-200 transition-colors">
                                    View
                                </a>
                                <button onclick="addToCart('food_item', <?php echo e($item->id); ?>)" class="bg-orange-600 text-white px-2 py-1 rounded text-xs hover:bg-orange-700 transition-colors">
                                    Add
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Food Items Pagination -->
            <div class="mb-8">
                <?php echo e($foodItems->appends(request()->query())->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-12">
                <i class="fas fa-utensils text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-600">No food items found matching your criteria.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    // Tab functionality
    function showTab(tabName) {
        // Hide all sections
        document.getElementById('packages-section').classList.add('hidden');
        document.getElementById('food-items-section').classList.add('hidden');

        // Remove active class from all tabs
        document.getElementById('packages-tab').classList.remove('text-orange-600', 'border-orange-600');
        document.getElementById('packages-tab').classList.add('text-gray-500', 'border-transparent');
        document.getElementById('food-items-tab').classList.remove('text-orange-600', 'border-orange-600');
        document.getElementById('food-items-tab').classList.add('text-gray-500', 'border-transparent');

        // Show selected section and activate tab
        if (tabName === 'packages') {
            document.getElementById('packages-section').classList.remove('hidden');
            document.getElementById('packages-tab').classList.add('text-orange-600', 'border-orange-600');
            document.getElementById('packages-tab').classList.remove('text-gray-500', 'border-transparent');
        } else {
            document.getElementById('food-items-section').classList.remove('hidden');
            document.getElementById('food-items-tab').classList.add('text-orange-600', 'border-orange-600');
            document.getElementById('food-items-tab').classList.remove('text-gray-500', 'border-transparent');
        }
    }

    // URL parameter update function
    function updateUrlParameter(param, value) {
        const url = new URL(window.location);
        if (value && value !== 'all' && value !== '') {
            url.searchParams.set(param, value);
        } else {
            url.searchParams.delete(param);
        }
        return url.toString();
    }

    // Initialize default tab
    document.addEventListener('DOMContentLoaded', function() {
        showTab('food-items');
    });
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\r56t7yihu\food-ordering-app\resources\views/menu/index.blade.php ENDPATH**/ ?>