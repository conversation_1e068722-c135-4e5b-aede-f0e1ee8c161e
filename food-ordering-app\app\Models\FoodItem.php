<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class FoodItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'ingredients',
        'image',
        'images',
        'price_per_unit',
        'price_per_kg',
        'unit',
        'category_id',
        'cuisine_id',
        'is_vegetarian',
        'is_vegan',
        'is_gluten_free',
        'is_spicy',
        'spice_level',
        'is_available',
        'is_popular',
        'is_featured',
        'allow_bulk_order',
        'calories_per_unit',
        'protein',
        'carbs',
        'fat',
        'preparation_time',
        'preparation_notes',
        'sort_order',
        'stock_quantity',
        'min_order_quantity',
        'max_order_quantity',
    ];

    protected $casts = [
        'images' => 'array',
        'is_vegetarian' => 'boolean',
        'is_vegan' => 'boolean',
        'is_gluten_free' => 'boolean',
        'is_spicy' => 'boolean',
        'is_available' => 'boolean',
        'is_popular' => 'boolean',
        'is_featured' => 'boolean',
        'allow_bulk_order' => 'boolean',
        'price_per_unit' => 'decimal:2',
        'price_per_kg' => 'decimal:2',
        'protein' => 'decimal:2',
        'carbs' => 'decimal:2',
        'fat' => 'decimal:2',
    ];

    /**
     * Get the category that owns the food item.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the cuisine that owns the food item.
     */
    public function cuisine(): BelongsTo
    {
        return $this->belongsTo(Cuisine::class);
    }

    /**
     * Get the cart items for this food item.
     */
    public function cartItems(): HasMany
    {
        return $this->hasMany(CartItem::class);
    }

    /**
     * Get the order items for this food item.
     */
    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the packages that contain this food item.
     */
    public function packages(): BelongsToMany
    {
        return $this->belongsToMany(Package::class, 'package_items')
                    ->withPivot('quantity', 'unit_weight', 'notes', 'is_optional', 'is_customizable', 'sort_order')
                    ->withTimestamps();
    }

    /**
     * Scope to get only available items.
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope to get only vegetarian items.
     */
    public function scopeVegetarian($query)
    {
        return $query->where('is_vegetarian', true);
    }

    /**
     * Scope to get only popular items.
     */
    public function scopePopular($query)
    {
        return $query->where('is_popular', true);
    }

    /**
     * Scope to get only featured items.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to filter by category.
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Scope to filter by cuisine.
     */
    public function scopeByCuisine($query, $cuisineId)
    {
        return $query->where('cuisine_id', $cuisineId);
    }

    /**
     * Scope to search by name or description.
     */
    public function scopeSearch($query, $term)
    {
        return $query->where(function ($q) use ($term) {
            $q->where('name', 'like', "%{$term}%")
              ->orWhere('description', 'like', "%{$term}%")
              ->orWhere('ingredients', 'like', "%{$term}%");
        });
    }

    /**
     * Get the effective price based on order type.
     */
    public function getEffectivePrice($orderType = 'unit', $weight = null)
    {
        if ($orderType === 'bulk' && $this->allow_bulk_order && $this->price_per_kg) {
            return $this->price_per_kg * ($weight ?? 1);
        }
        
        return $this->price_per_unit;
    }
}
