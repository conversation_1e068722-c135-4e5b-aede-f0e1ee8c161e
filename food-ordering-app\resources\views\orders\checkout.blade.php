@extends('layouts.app')

@section('title', 'Checkout - Food Ordering App')

@section('content')
<div class="container-mobile">
    <div class="py-6">
        <h1 class="text-2xl font-bold mb-6">Checkout</h1>
        
        <form action="{{ route('orders.place') }}" method="POST" class="space-y-6">
            @csrf
            
            <!-- Order Summary -->
            <div class="bg-white rounded-lg shadow-sm p-4">
                <h3 class="font-semibold text-lg mb-4">Order Summary</h3>
                
                <!-- Cart Items -->
                <div class="space-y-3 mb-4">
                    @foreach($cartItems as $cartItem)
                        @php
                            $item = $cartItem->foodItem ?? $cartItem->package;
                            $itemType = $cartItem->foodItem ? 'food_item' : 'package';
                        @endphp
                        <div class="flex items-center space-x-3 py-2 border-b border-gray-100 last:border-b-0">
                            <!-- Item Image -->
                            <div class="flex-shrink-0">
                                @if($item->image)
                                    <img src="{{ $item->image }}" alt="{{ $item->name }}" class="w-12 h-12 rounded-lg object-cover">
                                @else
                                    <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-image text-gray-400"></i>
                                    </div>
                                @endif
                            </div>
                            
                            <!-- Item Details -->
                            <div class="flex-1">
                                <h4 class="font-medium text-sm">{{ $item->name }}</h4>
                                <p class="text-xs text-gray-600">
                                    Qty: {{ $cartItem->quantity }} × ${{ number_format($cartItem->unit_price, 2) }}
                                </p>
                                @if($cartItem->special_instructions)
                                    <p class="text-xs text-orange-600 mt-1">
                                        <i class="fas fa-sticky-note"></i> {{ $cartItem->special_instructions }}
                                    </p>
                                @endif
                            </div>
                            
                            <!-- Item Total -->
                            <div class="text-right">
                                <span class="font-semibold">${{ number_format($cartItem->total_price, 2) }}</span>
                            </div>
                        </div>
                    @endforeach
                </div>
                
                <!-- Price Breakdown -->
                <div class="space-y-2 pt-4 border-t border-gray-200">
                    <div class="flex justify-between">
                        <span>Subtotal</span>
                        <span>${{ number_format($subtotal, 2) }}</span>
                    </div>
                    <div class="flex justify-between text-sm text-gray-600">
                        <span>Tax (10%)</span>
                        <span>${{ number_format($taxAmount, 2) }}</span>
                    </div>
                    <div class="flex justify-between text-sm text-gray-600">
                        <span>Delivery Fee</span>
                        <span>
                            @if($deliveryFee > 0)
                                ${{ number_format($deliveryFee, 2) }}
                            @else
                                <span class="text-green-600">FREE</span>
                            @endif
                        </span>
                    </div>
                    <hr class="my-2">
                    <div class="flex justify-between font-bold text-lg">
                        <span>Total</span>
                        <span class="text-orange-600">${{ number_format($total, 2) }}</span>
                    </div>
                </div>
            </div>
            
            <!-- Customer Information -->
            <div class="bg-white rounded-lg shadow-sm p-4">
                <h3 class="font-semibold text-lg mb-4">Customer Information</h3>
                
                <div class="space-y-4">
                    <div>
                        <label for="customer_name" class="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                        <input type="text" id="customer_name" name="customer_name" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                               value="{{ old('customer_name') }}">
                        @error('customer_name')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="customer_email" class="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
                        <input type="email" id="customer_email" name="customer_email" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                               value="{{ old('customer_email') }}">
                        @error('customer_email')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="customer_phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number *</label>
                        <input type="tel" id="customer_phone" name="customer_phone" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                               value="{{ old('customer_phone') }}">
                        @error('customer_phone')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>
            
            <!-- Order Type -->
            <div class="bg-white rounded-lg shadow-sm p-4">
                <h3 class="font-semibold text-lg mb-4">Order Type</h3>
                
                <div class="space-y-3">
                    <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="order_type" value="delivery" class="text-orange-600 focus:ring-orange-500" 
                               {{ old('order_type', 'delivery') === 'delivery' ? 'checked' : '' }}>
                        <div class="ml-3">
                            <div class="font-medium">Delivery</div>
                            <div class="text-sm text-gray-600">Get it delivered to your address</div>
                        </div>
                    </label>
                    
                    <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="order_type" value="pickup" class="text-orange-600 focus:ring-orange-500"
                               {{ old('order_type') === 'pickup' ? 'checked' : '' }}>
                        <div class="ml-3">
                            <div class="font-medium">Pickup</div>
                            <div class="text-sm text-gray-600">Pick up from our restaurant</div>
                        </div>
                    </label>
                </div>
                @error('order_type')
                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                @enderror
            </div>
            
            <!-- Delivery Address (shown when delivery is selected) -->
            <div id="delivery-section" class="bg-white rounded-lg shadow-sm p-4">
                <h3 class="font-semibold text-lg mb-4">Delivery Address</h3>
                
                <div class="space-y-4">
                    <div>
                        <label for="delivery_address" class="block text-sm font-medium text-gray-700 mb-1">Street Address *</label>
                        <textarea id="delivery_address" name="delivery_address" rows="2" required
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                  placeholder="Enter your full address">{{ old('delivery_address') }}</textarea>
                        @error('delivery_address')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="delivery_city" class="block text-sm font-medium text-gray-700 mb-1">City *</label>
                            <input type="text" id="delivery_city" name="delivery_city" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                   value="{{ old('delivery_city') }}">
                            @error('delivery_city')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="delivery_postal_code" class="block text-sm font-medium text-gray-700 mb-1">Postal Code *</label>
                            <input type="text" id="delivery_postal_code" name="delivery_postal_code" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                   value="{{ old('delivery_postal_code') }}">
                            @error('delivery_postal_code')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                    
                    <div>
                        <label for="delivery_instructions" class="block text-sm font-medium text-gray-700 mb-1">Delivery Instructions (Optional)</label>
                        <textarea id="delivery_instructions" name="delivery_instructions" rows="2"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                  placeholder="Any special instructions for delivery...">{{ old('delivery_instructions') }}</textarea>
                        @error('delivery_instructions')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>
            
            <!-- Payment Method -->
            <div class="bg-white rounded-lg shadow-sm p-4">
                <h3 class="font-semibold text-lg mb-4">Payment Method</h3>
                
                <div class="space-y-3">
                    <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="payment_method" value="cash" class="text-orange-600 focus:ring-orange-500"
                               {{ old('payment_method', 'cash') === 'cash' ? 'checked' : '' }}>
                        <div class="ml-3">
                            <div class="font-medium">Cash on Delivery</div>
                            <div class="text-sm text-gray-600">Pay with cash when your order arrives</div>
                        </div>
                    </label>
                    
                    <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="payment_method" value="card" class="text-orange-600 focus:ring-orange-500"
                               {{ old('payment_method') === 'card' ? 'checked' : '' }}>
                        <div class="ml-3">
                            <div class="font-medium">Credit/Debit Card</div>
                            <div class="text-sm text-gray-600">Pay with your card on delivery</div>
                        </div>
                    </label>
                    
                    <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="payment_method" value="online" class="text-orange-600 focus:ring-orange-500"
                               {{ old('payment_method') === 'online' ? 'checked' : '' }}>
                        <div class="ml-3">
                            <div class="font-medium">Online Payment</div>
                            <div class="text-sm text-gray-600">Pay online now (Coming Soon)</div>
                        </div>
                    </label>
                    
                    <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="payment_method" value="wallet" class="text-orange-600 focus:ring-orange-500"
                               {{ old('payment_method') === 'wallet' ? 'checked' : '' }}>
                        <div class="ml-3">
                            <div class="font-medium">Digital Wallet</div>
                            <div class="text-sm text-gray-600">Pay with mobile wallet (Coming Soon)</div>
                        </div>
                    </label>
                </div>
                @error('payment_method')
                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                @enderror
            </div>
            
            <!-- Action Buttons -->
            <div class="space-y-3">
                <button type="submit" 
                        class="w-full bg-orange-600 text-white py-3 rounded-lg font-semibold hover:bg-orange-700 transition-colors">
                    Place Order - ${{ number_format($total, 2) }}
                </button>
                
                <a href="{{ route('cart.index') }}" 
                   class="w-full bg-gray-200 text-gray-800 py-3 rounded-lg font-medium text-center block hover:bg-gray-300 transition-colors">
                    Back to Cart
                </a>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const orderTypeRadios = document.querySelectorAll('input[name="order_type"]');
    const deliverySection = document.getElementById('delivery-section');
    const deliveryInputs = deliverySection.querySelectorAll('input[required], textarea[required]');
    
    function toggleDeliverySection() {
        const selectedType = document.querySelector('input[name="order_type"]:checked').value;
        
        if (selectedType === 'delivery') {
            deliverySection.style.display = 'block';
            deliveryInputs.forEach(input => input.required = true);
        } else {
            deliverySection.style.display = 'none';
            deliveryInputs.forEach(input => input.required = false);
        }
    }
    
    orderTypeRadios.forEach(radio => {
        radio.addEventListener('change', toggleDeliverySection);
    });
    
    // Initialize on page load
    toggleDeliverySection();
});
</script>
@endsection
