<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Package extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'image',
        'images',
        'price',
        'original_price',
        'discount_percentage',
        'package_type',
        'serves_people',
        'category_id',
        'cuisine_id',
        'is_vegetarian',
        'is_vegan',
        'is_gluten_free',
        'is_available',
        'is_popular',
        'is_featured',
        'preparation_time',
        'available_from',
        'available_until',
        'sort_order',
        'stock_quantity',
        'min_order_quantity',
        'max_order_quantity',
    ];

    protected $casts = [
        'images' => 'array',
        'is_vegetarian' => 'boolean',
        'is_vegan' => 'boolean',
        'is_gluten_free' => 'boolean',
        'is_available' => 'boolean',
        'is_popular' => 'boolean',
        'is_featured' => 'boolean',
        'price' => 'decimal:2',
        'original_price' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'available_from' => 'datetime',
        'available_until' => 'datetime',
    ];

    /**
     * Get the category that owns the package.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the cuisine that owns the package.
     */
    public function cuisine(): BelongsTo
    {
        return $this->belongsTo(Cuisine::class);
    }

    /**
     * Get the food items in this package.
     */
    public function foodItems(): BelongsToMany
    {
        return $this->belongsToMany(FoodItem::class, 'package_items')
                    ->withPivot('quantity', 'unit_weight', 'notes', 'is_optional', 'is_customizable', 'sort_order')
                    ->withTimestamps()
                    ->orderBy('package_items.sort_order');
    }

    /**
     * Get the cart items for this package.
     */
    public function cartItems(): HasMany
    {
        return $this->hasMany(CartItem::class);
    }

    /**
     * Get the order items for this package.
     */
    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Scope to get only available packages.
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope to get only vegetarian packages.
     */
    public function scopeVegetarian($query)
    {
        return $query->where('is_vegetarian', true);
    }

    /**
     * Scope to get only popular packages.
     */
    public function scopePopular($query)
    {
        return $query->where('is_popular', true);
    }

    /**
     * Scope to get only featured packages.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to filter by package type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('package_type', $type);
    }

    /**
     * Scope to filter by category.
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Scope to filter by cuisine.
     */
    public function scopeByCuisine($query, $cuisineId)
    {
        return $query->where('cuisine_id', $cuisineId);
    }

    /**
     * Scope to search by name or description.
     */
    public function scopeSearch($query, $term)
    {
        return $query->where(function ($q) use ($term) {
            $q->where('name', 'like', "%{$term}%")
              ->orWhere('description', 'like', "%{$term}%");
        });
    }

    /**
     * Check if package is currently available based on time constraints.
     */
    public function isCurrentlyAvailable(): bool
    {
        if (!$this->is_available) {
            return false;
        }

        $now = now();
        
        if ($this->available_from && $now->lt($this->available_from)) {
            return false;
        }
        
        if ($this->available_until && $now->gt($this->available_until)) {
            return false;
        }
        
        return true;
    }

    /**
     * Calculate the total savings amount.
     */
    public function getSavingsAmount(): float
    {
        if ($this->original_price && $this->original_price > $this->price) {
            return $this->original_price - $this->price;
        }
        
        return 0;
    }
}
