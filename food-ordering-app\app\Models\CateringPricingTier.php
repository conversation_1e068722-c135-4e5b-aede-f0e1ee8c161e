<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CateringPricingTier extends Model
{
    use HasFactory;

    protected $fillable = [
        'catering_package_id',
        'min_guests',
        'max_guests',
        'price_per_person',
        'setup_fee',
        'service_fee',
        'delivery_fee',
        'tier_description',
    ];

    protected $casts = [
        'price_per_person' => 'decimal:2',
        'setup_fee' => 'decimal:2',
        'service_fee' => 'decimal:2',
        'delivery_fee' => 'decimal:2',
    ];

    /**
     * Get the catering package that owns this pricing tier.
     */
    public function cateringPackage(): BelongsTo
    {
        return $this->belongsTo(CateringPackage::class);
    }

    /**
     * Check if this tier applies to the given guest count.
     */
    public function appliesTo(int $guestCount): bool
    {
        return $guestCount >= $this->min_guests && $guestCount <= $this->max_guests;
    }

    /**
     * Get the guest range as a formatted string.
     */
    public function getGuestRangeAttribute(): string
    {
        if ($this->min_guests === $this->max_guests) {
            return $this->min_guests . ' guests';
        }
        
        return $this->min_guests . '-' . $this->max_guests . ' guests';
    }

    /**
     * Calculate total additional fees.
     */
    public function getTotalFeesAttribute(): float
    {
        return $this->setup_fee + $this->service_fee + $this->delivery_fee;
    }
}
