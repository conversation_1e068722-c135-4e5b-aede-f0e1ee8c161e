<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class LoginController extends Controller
{
    /**
     * Show the login form.
     */
    public function showLoginForm()
    {
        return view('auth.login');
    }

    /**
     * Handle a login request.
     */
    public function login(Request $request)
    {
        $request->validate([
            'login' => 'required|string',
            'password' => 'required',
        ]);

        $loginField = $request->login;
        $password = $request->password;
        $remember = $request->boolean('remember');

        // Determine if login field is email or mobile
        $fieldType = filter_var($loginField, FILTER_VALIDATE_EMAIL) ? 'email' : 'mobile';

        // If it's not a valid email, validate as mobile number
        if ($fieldType === 'mobile') {
            if (!preg_match('/^[0-9]{10}$/', $loginField)) {
                throw ValidationException::withMessages([
                    'login' => ['Please enter a valid mobile number (10 digits) or email address.'],
                ]);
            }
        }

        $credentials = [
            $fieldType => $loginField,
            'password' => $password,
        ];

        if (Auth::attempt($credentials, $remember)) {
            $request->session()->regenerate();

            // Update last login time
            Auth::user()->update(['last_login_at' => now()]);

            // Regular login is for customers only - redirect to home
            return redirect()->intended(route('home'));
        }

        throw ValidationException::withMessages([
            'login' => ['The provided credentials do not match our records.'],
        ]);
    }

    /**
     * Log the user out.
     */
    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('home');
    }
}
