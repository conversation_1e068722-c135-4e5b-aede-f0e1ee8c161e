<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class RegisterController extends Controller
{
    /**
     * Show the registration form.
     */
    public function showRegistrationForm()
    {
        return view('auth.register');
    }

    /**
     * Handle a registration request.
     */
    public function register(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'mobile' => [
                'required',
                'string',
                'regex:/^[0-9]{10}$/',
                'unique:users,mobile'
            ],
            'email' => 'nullable|email|max:255|unique:users,email',
            'password' => [
                'required',
                'confirmed',
                Password::min(8)
                    ->mixedCase()
                    ->numbers()
                    ->symbols()
            ],
            'address' => 'nullable|string|max:500',
            'terms' => 'required|accepted',
        ], [
            'mobile.regex' => 'Mobile number must be exactly 10 digits.',
            'mobile.unique' => 'This mobile number is already registered.',
            'password.min' => 'Password must be at least 8 characters.',
            'terms.required' => 'You must accept the terms and conditions.',
        ]);

        // Create the user
        $user = User::create([
            'name' => $request->name,
            'mobile' => $request->mobile,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'address' => $request->address,
            'role' => 'customer',
            'is_active' => true,
        ]);

        // Log the user in
        Auth::login($user);

        // Update last login time
        $user->update(['last_login_at' => now()]);

        return redirect()->route('home')->with('success', 'Registration successful! Welcome to our food ordering platform.');
    }

    /**
     * Check if mobile number is available.
     */
    public function checkMobile(Request $request)
    {
        $request->validate([
            'mobile' => 'required|string|regex:/^[0-9]{10}$/',
        ]);

        $exists = User::where('mobile', $request->mobile)->exists();

        return response()->json([
            'available' => !$exists,
            'message' => $exists ? 'This mobile number is already registered.' : 'Mobile number is available.'
        ]);
    }

    /**
     * Check if email is available.
     */
    public function checkEmail(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        $exists = User::where('email', $request->email)->exists();

        return response()->json([
            'available' => !$exists,
            'message' => $exists ? 'This email is already registered.' : 'Email is available.'
        ]);
    }
}
