<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CateringEventType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'base_price_per_person',
        'min_guests',
        'max_guests',
        'advance_booking_hours',
        'available_times',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'base_price_per_person' => 'decimal:2',
        'available_times' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the catering orders for this event type.
     */
    public function cateringOrders(): HasMany
    {
        return $this->hasMany(CateringOrder::class);
    }

    /**
     * Scope to get only active event types.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Check if the event type is available for the given guest count.
     */
    public function isAvailableForGuests(int $guestCount): bool
    {
        return $guestCount >= $this->min_guests && $guestCount <= $this->max_guests;
    }

    /**
     * Get the minimum advance booking time.
     */
    public function getMinimumBookingTime(): \Carbon\Carbon
    {
        return now()->addHours($this->advance_booking_hours);
    }

    /**
     * Check if a date/time is available for booking.
     */
    public function isDateTimeAvailable(\Carbon\Carbon $dateTime): bool
    {
        // Check if it's far enough in advance
        if ($dateTime->lt($this->getMinimumBookingTime())) {
            return false;
        }

        // Check if the time slot is available (if specific times are set)
        if ($this->available_times) {
            $timeSlot = $dateTime->format('H:i');
            return in_array($timeSlot, $this->available_times);
        }

        return true;
    }
}
