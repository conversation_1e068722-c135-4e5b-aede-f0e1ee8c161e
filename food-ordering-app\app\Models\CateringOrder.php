<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CateringOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_number',
        'user_id',
        'catering_event_type_id',
        'catering_package_id',
        'customer_name',
        'customer_email',
        'customer_phone',
        'company_name',
        'event_name',
        'event_date',
        'event_start_time',
        'event_end_time',
        'guest_count',
        'event_description',
        'event_address',
        'event_city',
        'event_state',
        'event_postal_code',
        'venue_details',
        'dietary_requirements',
        'special_requests',
        'needs_setup_service',
        'needs_cleanup_service',
        'needs_serving_staff',
        'serving_staff_count',
        'base_amount',
        'setup_fee',
        'service_fee',
        'delivery_fee',
        'staff_fee',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'status',
        'payment_status',
        'payment_method',
        'confirmed_at',
        'preparation_started_at',
        'ready_at',
        'delivered_at',
        'completed_at',
        'cancelled_at',
        'notes',
        'cancellation_reason',
        'status_history',
    ];

    protected $casts = [
        'event_date' => 'date',
        'event_start_time' => 'datetime:H:i',
        'event_end_time' => 'datetime:H:i',
        'dietary_requirements' => 'array',
        'special_requests' => 'array',
        'needs_setup_service' => 'boolean',
        'needs_cleanup_service' => 'boolean',
        'needs_serving_staff' => 'boolean',
        'base_amount' => 'decimal:2',
        'setup_fee' => 'decimal:2',
        'service_fee' => 'decimal:2',
        'delivery_fee' => 'decimal:2',
        'staff_fee' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'confirmed_at' => 'datetime',
        'preparation_started_at' => 'datetime',
        'ready_at' => 'datetime',
        'delivered_at' => 'datetime',
        'completed_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'status_history' => 'array',
    ];

    /**
     * Get the user that owns the catering order.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the event type for this catering order.
     */
    public function eventType(): BelongsTo
    {
        return $this->belongsTo(CateringEventType::class, 'catering_event_type_id');
    }

    /**
     * Get the package for this catering order.
     */
    public function package(): BelongsTo
    {
        return $this->belongsTo(CateringPackage::class, 'catering_package_id');
    }

    /**
     * Get the order items for this catering order.
     */
    public function orderItems(): HasMany
    {
        return $this->hasMany(CateringOrderItem::class);
    }

    /**
     * Generate a unique order number.
     */
    public static function generateOrderNumber(): string
    {
        do {
            $orderNumber = 'CAT-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
        } while (self::where('order_number', $orderNumber)->exists());

        return $orderNumber;
    }

    /**
     * Update the order status and add to history.
     */
    public function updateStatus(string $newStatus, ?string $notes = null): void
    {
        $oldStatus = $this->status;
        $this->status = $newStatus;

        // Add to status history
        $history = $this->status_history ?? [];
        $history[] = [
            'from' => $oldStatus,
            'to' => $newStatus,
            'timestamp' => now()->toISOString(),
            'notes' => $notes,
        ];
        $this->status_history = $history;

        // Set specific timestamps
        switch ($newStatus) {
            case 'confirmed':
                $this->confirmed_at = now();
                break;
            case 'in_preparation':
                $this->preparation_started_at = now();
                break;
            case 'ready':
                $this->ready_at = now();
                break;
            case 'delivered':
                $this->delivered_at = now();
                break;
            case 'completed':
                $this->completed_at = now();
                break;
            case 'cancelled':
                $this->cancelled_at = now();
                if ($notes) {
                    $this->cancellation_reason = $notes;
                }
                break;
        }

        $this->save();
    }

    /**
     * Check if the order can be cancelled.
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'confirmed']);
    }

    /**
     * Check if the order is completed.
     */
    public function isCompleted(): bool
    {
        return in_array($this->status, ['completed', 'cancelled']);
    }

    /**
     * Get the event date and time formatted.
     */
    public function getEventDateTimeAttribute(): string
    {
        return $this->event_date->format('M j, Y') . ' at ' . $this->event_start_time->format('g:i A');
    }

    /**
     * Get the full event address.
     */
    public function getFullAddressAttribute(): string
    {
        $address = $this->event_address . ', ' . $this->event_city;
        if ($this->event_state) {
            $address .= ', ' . $this->event_state;
        }
        $address .= ' ' . $this->event_postal_code;
        return $address;
    }

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by event date.
     */
    public function scopeByEventDate($query, $date)
    {
        return $query->where('event_date', $date);
    }

    /**
     * Scope to get upcoming events.
     */
    public function scopeUpcoming($query)
    {
        return $query->where('event_date', '>=', now()->toDateString());
    }
}
