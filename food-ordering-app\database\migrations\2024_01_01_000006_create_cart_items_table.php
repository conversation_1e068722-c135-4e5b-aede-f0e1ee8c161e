<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cart_items', function (Blueprint $table) {
            $table->id();
            $table->string('session_id'); // For guest users
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade'); // For logged-in users

            // Item details - either food item or package
            $table->foreignId('food_item_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('package_id')->nullable()->constrained()->onDelete('cascade');

            // Quantity and pricing
            $table->integer('quantity')->default(1);
            $table->decimal('unit_weight', 8, 3)->nullable(); // For bulk ordering in kg
            $table->decimal('unit_price', 10, 2); // Price at time of adding to cart
            $table->decimal('total_price', 10, 2); // Calculated total

            // Order type
            $table->enum('order_type', ['unit', 'bulk'])->default('unit');

            // Customizations
            $table->json('customizations')->nullable(); // Store any customizations
            $table->text('special_instructions')->nullable();

            $table->timestamps();

            // Indexes
            $table->index(['session_id']);
            $table->index(['user_id']);
            $table->index(['food_item_id']);
            $table->index(['package_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cart_items');
    }
};
