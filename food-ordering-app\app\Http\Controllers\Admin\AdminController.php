<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Order;
use App\Models\FoodItem;
use App\Models\Category;
use App\Models\Cuisine;
use App\Models\Package;
use App\Models\CateringOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminController extends Controller
{
    /**
     * Display the admin dashboard.
     */
    public function index()
    {
        // Get key metrics
        $totalUsers = User::where('role', 'customer')->count();
        $totalOrders = Order::count();
        $totalRevenue = Order::where('payment_status', 'paid')->sum('total_amount');
        $totalFoodItems = FoodItem::count();
        $totalCategories = Category::count();
        $totalCuisines = Cuisine::count();
        $totalPackages = Package::count();
        $totalCateringOrders = CateringOrder::count();

        // Recent orders
        $recentOrders = Order::with(['user'])
            ->latest()
            ->limit(10)
            ->get();

        // Monthly revenue chart data
        $monthlyRevenue = Order::where('payment_status', 'paid')
            ->where('created_at', '>=', Carbon::now()->subMonths(12))
            ->select(
                DB::raw('YEAR(created_at) as year'),
                DB::raw('MONTH(created_at) as month'),
                DB::raw('SUM(total_amount) as revenue')
            )
            ->groupBy('year', 'month')
            ->orderBy('year', 'asc')
            ->orderBy('month', 'asc')
            ->get();

        // Order status distribution
        $orderStatusData = Order::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get();

        // Top selling food items
        $topFoodItems = FoodItem::withCount(['orderItems'])
            ->orderBy('order_items_count', 'desc')
            ->limit(10)
            ->get();

        // Recent users
        $recentUsers = User::where('role', 'customer')
            ->latest()
            ->limit(5)
            ->get();

        return view('admin.dashboard', compact(
            'totalUsers',
            'totalOrders',
            'totalRevenue',
            'totalFoodItems',
            'totalCategories',
            'totalCuisines',
            'totalPackages',
            'totalCateringOrders',
            'recentOrders',
            'monthlyRevenue',
            'orderStatusData',
            'topFoodItems',
            'recentUsers'
        ));
    }
}
