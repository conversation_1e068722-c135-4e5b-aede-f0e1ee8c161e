<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>Admin Login - {{ config('app.name', 'Food Ordering App') }}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .admin-login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        .admin-badge {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.875rem;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 1rem;
        }
        .btn-admin {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            font-weight: 600;
        }
        .btn-admin:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            color: white;
        }

        /* Mobile-first responsive styles */
        @media (max-width: 576px) {
            .admin-login-card .card-body {
                padding: 1.5rem !important;
            }

            .admin-login-card h2 {
                font-size: 1.5rem;
            }

            .admin-badge {
                font-size: 0.8rem;
                padding: 0.4rem 0.8rem;
            }

            .demo-credentials {
                font-size: 0.875rem;
            }

            .demo-credentials .row > div {
                margin-bottom: 0.75rem;
            }

            .security-notice {
                font-size: 0.8rem;
            }
        }

        @media (min-width: 577px) and (max-width: 768px) {
            .admin-login-card .card-body {
                padding: 2.5rem !important;
            }
        }

        /* Touch-friendly form elements */
        @media (max-width: 768px) {
            .form-control, .btn {
                min-height: 48px;
            }

            .input-group-text {
                min-width: 48px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100 px-3">
            <div class="col-12 col-sm-10 col-md-8 col-lg-6 col-xl-4">
                <div class="card admin-login-card">
                    <div class="card-body p-3 p-sm-4 p-md-5">
                        <div class="text-center mb-4">
                            <div class="admin-badge">
                                <i class="bi bi-shield-lock me-1"></i>
                                Admin Access
                            </div>
                            <h2 class="fw-bold text-primary">Admin Panel</h2>
                            <p class="text-muted">Sign in to access admin dashboard</p>
                        </div>

                        @if($errors->any())
                            <div class="alert alert-danger">
                                @foreach($errors->all() as $error)
                                    <div>{{ $error }}</div>
                                @endforeach
                            </div>
                        @endif

                        @if(session('error'))
                            <div class="alert alert-danger">
                                {{ session('error') }}
                            </div>
                        @endif

                        @if(session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif

                        <form method="POST" action="{{ route('admin.login') }}">
                            @csrf

                            <div class="mb-3">
                                <label for="email" class="form-label">Admin Email or Mobile</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-person-badge"></i>
                                    </span>
                                    <input type="text" class="form-control @error('email') is-invalid @enderror"
                                           id="email" name="email" value="{{ old('email') }}" required autofocus
                                           placeholder="Enter admin email or mobile number">
                                </div>
                                @error('email')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-key"></i>
                                    </span>
                                    <input type="password" class="form-control @error('password') is-invalid @enderror"
                                           id="password" name="password" required
                                           placeholder="Enter password">
                                </div>
                                @error('password')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    Keep me signed in
                                </label>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-admin btn-lg">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>
                                    Access Admin Panel
                                </button>
                            </div>
                        </form>

                        <hr class="my-4">

                        <div class="text-center">
                            <a href="{{ route('home') }}" class="text-decoration-none">
                                <i class="bi bi-arrow-left me-1"></i>
                                Back to Website
                            </a>
                        </div>

                        <!-- Demo Credentials -->
                        <div class="mt-4 p-3 bg-light rounded demo-credentials">
                            <!-- Collapsible header for mobile -->
                            <div class="d-block d-md-none">
                                <button class="btn btn-link p-0 text-decoration-none fw-bold" type="button"
                                        data-bs-toggle="collapse" data-bs-target="#adminDemoCredentials"
                                        aria-expanded="false" aria-controls="adminDemoCredentials">
                                    <i class="bi bi-chevron-down me-1"></i>
                                    Demo Admin Credentials
                                </button>
                                <div class="collapse" id="adminDemoCredentials">
                                    <div class="mt-2">
                                        <div class="mb-2">
                                            <strong>Super Admin:</strong><br>
                                            <small class="text-muted"><EMAIL> or 9999999999<br>password123</small>
                                        </div>
                                        <div class="mb-2">
                                            <strong>Manager:</strong><br>
                                            <small class="text-muted"><EMAIL> or 8888888888<br>password123</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Always visible on desktop -->
                            <div class="d-none d-md-block">
                                <h6 class="fw-bold mb-2">
                                    <i class="bi bi-info-circle me-1"></i>
                                    Demo Admin Credentials:
                                </h6>
                                <div class="row">
                                    <div class="col-12 mb-2">
                                        <strong>Super Admin:</strong><br>
                                        <small class="text-muted"><EMAIL> or 9999999999</small><br>
                                        <small class="text-muted">password123</small>
                                    </div>
                                    <div class="col-12">
                                        <strong>Manager:</strong><br>
                                        <small class="text-muted"><EMAIL> or 8888888888</small><br>
                                        <small class="text-muted">password123</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Security Notice -->
                        <div class="mt-3 p-2 bg-warning bg-opacity-10 rounded security-notice">
                            <small class="text-warning">
                                <i class="bi bi-exclamation-triangle me-1"></i>
                                <strong>Security Notice:</strong> This is an admin-only area. All access attempts are logged.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
