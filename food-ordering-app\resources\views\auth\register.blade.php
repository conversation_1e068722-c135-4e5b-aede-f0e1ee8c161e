@extends('layouts.app')

@section('title', 'Register - Food Ordering App')

@push('styles')
<style>
    .auth-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: calc(100vh - 140px); /* Account for navbar and footer */
        display: flex;
        align-items: center;
        padding: 2rem 0;
    }

    .register-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: none;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border-radius: 1rem;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-register {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        font-weight: 600;
    }

    .btn-register:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        color: white;
    }

    .password-strength {
        height: 4px;
        border-radius: 2px;
        margin-top: 5px;
        transition: all 0.3s ease;
    }

    .strength-weak { background-color: #dc3545; }
    .strength-medium { background-color: #ffc107; }
    .strength-strong { background-color: #28a745; }

    .field-validation {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .field-validation.valid {
        color: #28a745;
    }

    .field-validation.invalid {
        color: #dc3545;
    }

    /* Mobile-first responsive styles */
    @media (max-width: 576px) {
        .register-card .card-body {
            padding: 1.5rem !important;
        }

        .register-card h2 {
            font-size: 1.5rem;
        }

        /* Stack form fields on mobile */
        .mobile-stack .col-md-6 {
            margin-bottom: 1rem;
        }

        .password-requirements {
            font-size: 0.8rem;
        }

        .password-requirements .small {
            font-size: 0.75rem;
        }

        .auth-container {
            min-height: calc(100vh - 120px);
            padding: 1rem 0;
        }
    }

    @media (min-width: 577px) and (max-width: 768px) {
        .register-card .card-body {
            padding: 2.5rem !important;
        }
    }

    /* Touch-friendly form elements */
    @media (max-width: 768px) {
        .form-control, .btn {
            min-height: 48px;
        }

        .input-group-text {
            min-width: 48px;
            justify-content: center;
        }

        textarea.form-control {
            min-height: 80px;
        }
    }
</style>
@endpush

@section('content')
<div class="auth-container">
    <div class="container-mobile">
        <div class="flex justify-center">
            <div class="w-full max-w-2xl">
                <div class="card register-card">
                    <div class="card-body p-3 p-sm-4 p-md-5">
                        <div class="text-center mb-4">
                            <h2 class="fw-bold text-primary">Create Account</h2>
                            <p class="text-muted">Join us and start ordering delicious food</p>
                        </div>

                        @if($errors->any())
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        <form method="POST" action="{{ route('register') }}" id="registerForm">
                            @csrf

                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-person"></i>
                                        </span>
                                        <input type="text" class="form-control @error('name') is-invalid @enderror"
                                               id="name" name="name" value="{{ old('name') }}" required
                                               placeholder="Enter your full name">
                                    </div>
                                    @error('name')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row mobile-stack">
                                <div class="col-md-6 mb-3">
                                    <label for="mobile" class="form-label">Mobile Number <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-phone"></i>
                                        </span>
                                        <input type="tel" class="form-control @error('mobile') is-invalid @enderror"
                                               id="mobile" name="mobile" value="{{ old('mobile') }}" required
                                               placeholder="1234567890" maxlength="10" pattern="[0-9]{10}">
                                    </div>
                                    <div id="mobileValidation" class="field-validation"></div>
                                    @error('mobile')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">10-digit mobile number (used for login)</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email Address</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-envelope"></i>
                                        </span>
                                        <input type="email" class="form-control @error('email') is-invalid @enderror"
                                               id="email" name="email" value="{{ old('email') }}"
                                               placeholder="<EMAIL>">
                                    </div>
                                    <div id="emailValidation" class="field-validation"></div>
                                    @error('email')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">Optional (for order notifications)</small>
                                </div>
                            </div>

                            <div class="row mobile-stack">
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-lock"></i>
                                        </span>
                                        <input type="password" class="form-control @error('password') is-invalid @enderror"
                                               id="password" name="password" required
                                               placeholder="Create a strong password">
                                        <button type="button" class="btn btn-outline-secondary" id="togglePassword">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                    </div>
                                    <div class="password-strength" id="passwordStrength"></div>
                                    <div id="passwordRequirements" class="mt-2 password-requirements">
                                        <small class="text-muted">Password must contain:</small>
                                        <div class="small">
                                            <div id="req-length" class="text-muted">• At least 8 characters</div>
                                            <div id="req-upper" class="text-muted">• One uppercase letter</div>
                                            <div id="req-lower" class="text-muted">• One lowercase letter</div>
                                            <div id="req-number" class="text-muted">• One number</div>
                                            <div id="req-symbol" class="text-muted">• One special character</div>
                                        </div>
                                    </div>
                                    @error('password')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="password_confirmation" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-lock-fill"></i>
                                        </span>
                                        <input type="password" class="form-control"
                                               id="password_confirmation" name="password_confirmation" required
                                               placeholder="Confirm your password">
                                    </div>
                                    <div id="passwordMatch" class="field-validation"></div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="address" class="form-label">Address</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-geo-alt"></i>
                                    </span>
                                    <textarea class="form-control @error('address') is-invalid @enderror"
                                              id="address" name="address" rows="2"
                                              placeholder="Enter your delivery address (optional)">{{ old('address') }}</textarea>
                                </div>
                                @error('address')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">This will be your default delivery address</small>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input @error('terms') is-invalid @enderror"
                                       id="terms" name="terms" required>
                                <label class="form-check-label" for="terms">
                                    I agree to the <a href="#" class="text-decoration-none">Terms and Conditions</a>
                                    and <a href="#" class="text-decoration-none">Privacy Policy</a>
                                    <span class="text-danger">*</span>
                                </label>
                                @error('terms')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-register btn-lg" id="submitBtn">
                                    <i class="bi bi-person-plus me-2"></i>
                                    Create Account
                                </button>
                            </div>
                        </form>

                        <hr class="my-4">

                        <div class="text-center">
                            <p class="mb-3">Already have an account?</p>
                            <a href="{{ route('login') }}" class="btn btn-outline-primary">
                                <i class="bi bi-box-arrow-in-right me-1"></i>
                                Sign In
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')

    <script>
        // Password visibility toggle
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');

            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('bi-eye');
                icon.classList.add('bi-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('bi-eye-slash');
                icon.classList.add('bi-eye');
            }
        });

        // Password strength checker
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.getElementById('passwordStrength');

            // Check requirements
            const requirements = {
                length: password.length >= 8,
                upper: /[A-Z]/.test(password),
                lower: /[a-z]/.test(password),
                number: /[0-9]/.test(password),
                symbol: /[^A-Za-z0-9]/.test(password)
            };

            // Update requirement indicators
            Object.keys(requirements).forEach(req => {
                const element = document.getElementById(`req-${req}`);
                if (requirements[req]) {
                    element.classList.remove('text-muted');
                    element.classList.add('text-success');
                } else {
                    element.classList.remove('text-success');
                    element.classList.add('text-muted');
                }
            });

            // Calculate strength
            const score = Object.values(requirements).filter(Boolean).length;

            if (password.length === 0) {
                strengthBar.style.width = '0%';
                strengthBar.className = 'password-strength';
            } else if (score < 3) {
                strengthBar.style.width = '33%';
                strengthBar.className = 'password-strength strength-weak';
            } else if (score < 5) {
                strengthBar.style.width = '66%';
                strengthBar.className = 'password-strength strength-medium';
            } else {
                strengthBar.style.width = '100%';
                strengthBar.className = 'password-strength strength-strong';
            }
        });

        // Password confirmation checker
        document.getElementById('password_confirmation').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmation = this.value;
            const matchDiv = document.getElementById('passwordMatch');

            if (confirmation.length === 0) {
                matchDiv.textContent = '';
                matchDiv.className = 'field-validation';
            } else if (password === confirmation) {
                matchDiv.textContent = '✓ Passwords match';
                matchDiv.className = 'field-validation valid';
            } else {
                matchDiv.textContent = '✗ Passwords do not match';
                matchDiv.className = 'field-validation invalid';
            }
        });

        // Mobile number validation
        document.getElementById('mobile').addEventListener('input', function() {
            const mobile = this.value;
            const validationDiv = document.getElementById('mobileValidation');

            if (mobile.length === 0) {
                validationDiv.textContent = '';
                validationDiv.className = 'field-validation';
            } else if (/^[0-9]{10}$/.test(mobile)) {
                // Check availability via AJAX
                fetch('{{ route("check.mobile") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ mobile: mobile })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.available) {
                        validationDiv.textContent = '✓ Mobile number is available';
                        validationDiv.className = 'field-validation valid';
                    } else {
                        validationDiv.textContent = '✗ This mobile number is already registered';
                        validationDiv.className = 'field-validation invalid';
                    }
                });
            } else {
                validationDiv.textContent = '✗ Please enter a valid 10-digit mobile number';
                validationDiv.className = 'field-validation invalid';
            }
        });

        // Email validation
        document.getElementById('email').addEventListener('input', function() {
            const email = this.value;
            const validationDiv = document.getElementById('emailValidation');

            if (email.length === 0) {
                validationDiv.textContent = '';
                validationDiv.className = 'field-validation';
            } else if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                // Check availability via AJAX
                fetch('{{ route("check.email") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ email: email })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.available) {
                        validationDiv.textContent = '✓ Email is available';
                        validationDiv.className = 'field-validation valid';
                    } else {
                        validationDiv.textContent = '✗ This email is already registered';
                        validationDiv.className = 'field-validation invalid';
                    }
                });
            } else {
                validationDiv.textContent = '✗ Please enter a valid email address';
                validationDiv.className = 'field-validation invalid';
            }
        });

        // Only allow numbers in mobile field
        document.getElementById('mobile').addEventListener('keypress', function(e) {
            if (!/[0-9]/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'Escape', 'Enter'].includes(e.key)) {
                e.preventDefault();
            }
        });
    </script>
@endpush
