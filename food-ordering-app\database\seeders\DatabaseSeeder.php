<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed the database with sample data
        $this->call([
            AdminSeeder::class,
            CuisineSeeder::class,
            CategorySeeder::class,
            FoodItemSeeder::class,
            PackageSeeder::class,
            CateringSeeder::class,
        ]);

        // Create a test user
        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);
    }
}
