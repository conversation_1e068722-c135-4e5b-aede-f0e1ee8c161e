<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CateringEventType;
use App\Models\CateringPackage;
use App\Models\CateringPricingTier;

class CateringSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Event Types
        $eventTypes = [
            [
                'name' => 'Corporate Events',
                'slug' => 'corporate-events',
                'description' => 'Professional catering for business meetings, conferences, and corporate gatherings',
                'icon' => 'fas fa-building',
                'base_price_per_person' => 25.00,
                'min_guests' => 10,
                'max_guests' => 500,
                'advance_booking_hours' => 48,
                'available_times' => ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00'],
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Weddings',
                'slug' => 'weddings',
                'description' => 'Elegant catering services for your special day',
                'icon' => 'fas fa-heart',
                'base_price_per_person' => 45.00,
                'min_guests' => 20,
                'max_guests' => 300,
                'advance_booking_hours' => 168, // 1 week
                'available_times' => ['11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00'],
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Birthday Parties',
                'slug' => 'birthday-parties',
                'description' => 'Fun and delicious catering for birthday celebrations',
                'icon' => 'fas fa-birthday-cake',
                'base_price_per_person' => 20.00,
                'min_guests' => 5,
                'max_guests' => 100,
                'advance_booking_hours' => 24,
                'available_times' => ['10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00'],
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Social Gatherings',
                'slug' => 'social-gatherings',
                'description' => 'Perfect for family reunions, holiday parties, and social events',
                'icon' => 'fas fa-users',
                'base_price_per_person' => 22.00,
                'min_guests' => 10,
                'max_guests' => 200,
                'advance_booking_hours' => 48,
                'available_times' => ['10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00'],
                'is_active' => true,
                'sort_order' => 4,
            ],
        ];

        foreach ($eventTypes as $eventType) {
            CateringEventType::create($eventType);
        }

        // Create Catering Packages
        $packages = [
            [
                'name' => 'Executive Lunch Package',
                'slug' => 'executive-lunch-package',
                'description' => 'Professional lunch catering perfect for business meetings and corporate events. Features gourmet sandwiches, fresh salads, and premium sides.',
                'short_description' => 'Gourmet lunch catering for business meetings',
                'image_url' => null,
                'base_price_per_person' => 28.00,
                'min_guests' => 10,
                'max_guests' => 100,
                'included_items' => [
                    ['name' => 'Gourmet Sandwich Selection', 'description' => 'Assorted premium sandwiches', 'quantity' => 1],
                    ['name' => 'Fresh Garden Salad', 'description' => 'Mixed greens with seasonal vegetables', 'quantity' => 1],
                    ['name' => 'Artisan Chips', 'description' => 'Premium kettle-cooked chips', 'quantity' => 1],
                    ['name' => 'Fresh Fruit', 'description' => 'Seasonal fruit selection', 'quantity' => 1],
                    ['name' => 'Beverages', 'description' => 'Coffee, tea, and soft drinks', 'quantity' => 1],
                ],
                'dietary_options' => ['Vegetarian', 'Gluten-Free Available'],
                'service_includes' => ['Setup', 'Serving Utensils', 'Napkins & Plates'],
                'preparation_time_hours' => 24,
                'is_popular' => true,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Wedding Celebration Package',
                'slug' => 'wedding-celebration-package',
                'description' => 'Elegant multi-course dining experience for your wedding celebration. Includes appetizers, main course, sides, and dessert service.',
                'short_description' => 'Elegant multi-course wedding dining',
                'image_url' => null,
                'base_price_per_person' => 65.00,
                'min_guests' => 20,
                'max_guests' => 300,
                'included_items' => [
                    ['name' => 'Cocktail Hour Appetizers', 'description' => 'Assorted hors d\'oeuvres', 'quantity' => 1],
                    ['name' => 'Choice of Entree', 'description' => 'Chicken, beef, or vegetarian option', 'quantity' => 1],
                    ['name' => 'Seasonal Vegetables', 'description' => 'Chef\'s selection of vegetables', 'quantity' => 1],
                    ['name' => 'Starch Selection', 'description' => 'Rice, potatoes, or pasta', 'quantity' => 1],
                    ['name' => 'Wedding Cake Service', 'description' => 'Cake cutting and serving', 'quantity' => 1],
                ],
                'dietary_options' => ['Vegetarian', 'Vegan', 'Gluten-Free'],
                'service_includes' => ['Professional Service Staff', 'Linens', 'China & Glassware', 'Setup & Cleanup'],
                'preparation_time_hours' => 72,
                'is_popular' => true,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Party Buffet Package',
                'slug' => 'party-buffet-package',
                'description' => 'Fun and casual buffet-style catering perfect for birthday parties and social gatherings. Great variety to please all ages.',
                'short_description' => 'Casual buffet for parties and gatherings',
                'image_url' => null,
                'base_price_per_person' => 24.00,
                'min_guests' => 15,
                'max_guests' => 150,
                'included_items' => [
                    ['name' => 'Pizza Selection', 'description' => 'Assorted pizza varieties', 'quantity' => 1],
                    ['name' => 'Chicken Wings', 'description' => 'Buffalo and BBQ wings', 'quantity' => 1],
                    ['name' => 'Garden Salad', 'description' => 'Fresh mixed greens', 'quantity' => 1],
                    ['name' => 'Garlic Bread', 'description' => 'Fresh baked garlic bread', 'quantity' => 1],
                    ['name' => 'Soft Drinks', 'description' => 'Assorted sodas and juices', 'quantity' => 1],
                ],
                'dietary_options' => ['Vegetarian', 'Kid-Friendly'],
                'service_includes' => ['Buffet Setup', 'Chafing Dishes', 'Serving Utensils'],
                'preparation_time_hours' => 24,
                'is_popular' => false,
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Gourmet Dinner Package',
                'slug' => 'gourmet-dinner-package',
                'description' => 'Upscale dinner catering featuring premium ingredients and sophisticated presentation. Perfect for special occasions and formal events.',
                'short_description' => 'Upscale dinner with premium ingredients',
                'image_url' => null,
                'base_price_per_person' => 55.00,
                'min_guests' => 20,
                'max_guests' => 200,
                'included_items' => [
                    ['name' => 'Artisan Appetizers', 'description' => 'Chef\'s selection of gourmet appetizers', 'quantity' => 1],
                    ['name' => 'Premium Entree', 'description' => 'Filet mignon, salmon, or vegetarian option', 'quantity' => 1],
                    ['name' => 'Gourmet Sides', 'description' => 'Roasted vegetables and specialty starches', 'quantity' => 1],
                    ['name' => 'Artisan Bread', 'description' => 'Fresh baked bread selection', 'quantity' => 1],
                    ['name' => 'Dessert Course', 'description' => 'Chef\'s signature dessert', 'quantity' => 1],
                ],
                'dietary_options' => ['Vegetarian', 'Vegan', 'Gluten-Free', 'Keto-Friendly'],
                'service_includes' => ['White Glove Service', 'Premium Linens', 'Fine China', 'Professional Staff'],
                'preparation_time_hours' => 48,
                'is_popular' => true,
                'is_active' => true,
                'sort_order' => 4,
            ],
        ];

        foreach ($packages as $packageData) {
            $package = CateringPackage::create($packageData);

            // Create pricing tiers for each package
            $this->createPricingTiers($package);
        }
    }

    /**
     * Create pricing tiers for a package.
     */
    private function createPricingTiers(CateringPackage $package): void
    {
        $basePricePerPerson = $package->base_price_per_person;
        
        // Create tiered pricing based on guest count
        $tiers = [
            [
                'min_guests' => $package->min_guests,
                'max_guests' => 25,
                'price_per_person' => $basePricePerPerson,
                'setup_fee' => 50.00,
                'service_fee' => 25.00,
                'delivery_fee' => 30.00,
                'tier_description' => 'Small group pricing',
            ],
            [
                'min_guests' => 26,
                'max_guests' => 50,
                'price_per_person' => $basePricePerPerson * 0.95, // 5% discount
                'setup_fee' => 75.00,
                'service_fee' => 40.00,
                'delivery_fee' => 25.00,
                'tier_description' => 'Medium group pricing with 5% discount',
            ],
            [
                'min_guests' => 51,
                'max_guests' => 100,
                'price_per_person' => $basePricePerPerson * 0.90, // 10% discount
                'setup_fee' => 100.00,
                'service_fee' => 60.00,
                'delivery_fee' => 20.00,
                'tier_description' => 'Large group pricing with 10% discount',
            ],
            [
                'min_guests' => 101,
                'max_guests' => $package->max_guests,
                'price_per_person' => $basePricePerPerson * 0.85, // 15% discount
                'setup_fee' => 150.00,
                'service_fee' => 100.00,
                'delivery_fee' => 15.00,
                'tier_description' => 'Extra large group pricing with 15% discount',
            ],
        ];

        foreach ($tiers as $tier) {
            // Only create tier if it's within the package's guest range
            if ($tier['max_guests'] >= $package->min_guests && $tier['min_guests'] <= $package->max_guests) {
                // Adjust the tier boundaries to fit within package limits
                $tier['min_guests'] = max($tier['min_guests'], $package->min_guests);
                $tier['max_guests'] = min($tier['max_guests'], $package->max_guests);
                
                CateringPricingTier::create([
                    'catering_package_id' => $package->id,
                    'min_guests' => $tier['min_guests'],
                    'max_guests' => $tier['max_guests'],
                    'price_per_person' => $tier['price_per_person'],
                    'setup_fee' => $tier['setup_fee'],
                    'service_fee' => $tier['service_fee'],
                    'delivery_fee' => $tier['delivery_fee'],
                    'tier_description' => $tier['tier_description'],
                ]);
            }
        }
    }
}
