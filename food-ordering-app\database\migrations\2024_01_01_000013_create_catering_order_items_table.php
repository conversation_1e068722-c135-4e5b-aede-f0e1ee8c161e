<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('catering_order_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('catering_order_id');
            $table->string('item_type'); // 'food_item', 'package_item', 'custom_item'
            $table->unsignedBigInteger('item_id')->nullable(); // Reference to food_items or other tables
            $table->string('item_name'); // Store name for historical purposes
            $table->text('item_description')->nullable();
            $table->integer('quantity');
            $table->decimal('unit_price', 8, 2);
            $table->decimal('total_price', 10, 2);
            $table->json('customizations')->nullable(); // Special preparations, modifications
            $table->text('special_instructions')->nullable();
            $table->timestamps();

            $table->index(['catering_order_id', 'item_type'], 'catering_order_items_order_type_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('catering_order_items');
    }
};
