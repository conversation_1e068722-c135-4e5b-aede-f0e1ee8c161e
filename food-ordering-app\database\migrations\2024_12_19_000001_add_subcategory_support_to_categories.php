<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Category;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if parent_id column already exists
        if (!Schema::hasColumn('categories', 'parent_id')) {
            Schema::table('categories', function (Blueprint $table) {
                $table->unsignedBigInteger('parent_id')->nullable()->after('image');
                $table->foreign('parent_id')->references('id')->on('categories')->onDelete('cascade');
                $table->index(['parent_id', 'is_active', 'sort_order']);
            });

            // Create sample subcategories only after adding the column
            $this->createSampleSubcategories();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove subcategories first
        Category::whereNotNull('parent_id')->delete();
        
        // Drop the foreign key and column
        if (Schema::hasColumn('categories', 'parent_id')) {
            Schema::table('categories', function (Blueprint $table) {
                $table->dropForeign(['parent_id']);
                $table->dropIndex(['parent_id', 'is_active', 'sort_order']);
                $table->dropColumn('parent_id');
            });
        }
    }

    /**
     * Create sample subcategories for existing main categories.
     */
    private function createSampleSubcategories(): void
    {
        // Get existing categories to create subcategories for
        $mainCategories = Category::whereNull('parent_id')->get();

        foreach ($mainCategories as $category) {
            $subcategories = $this->getSubcategoriesForCategory($category->name);
            
            foreach ($subcategories as $index => $subcategoryName) {
                Category::create([
                    'name' => $subcategoryName,
                    'slug' => \Illuminate\Support\Str::slug($subcategoryName),
                    'description' => "Delicious {$subcategoryName} from our {$category->name} collection",
                    'parent_id' => $category->id,
                    'is_active' => true,
                    'sort_order' => $index + 1,
                ]);
            }
        }
    }

    /**
     * Get subcategories based on main category name.
     */
    private function getSubcategoriesForCategory(string $categoryName): array
    {
        $subcategoriesMap = [
            'Appetizers' => ['Hot Appetizers', 'Cold Appetizers', 'Finger Foods', 'Dips & Spreads'],
            'Main Course' => ['Grilled Items', 'Curries', 'Rice Dishes', 'Pasta', 'Seafood'],
            'Desserts' => ['Cakes', 'Ice Cream', 'Traditional Sweets', 'Pastries', 'Puddings'],
            'Beverages' => ['Hot Drinks', 'Cold Drinks', 'Juices', 'Smoothies', 'Alcoholic'],
            'Salads' => ['Green Salads', 'Fruit Salads', 'Protein Salads', 'Grain Salads'],
            'Soups' => ['Hot Soups', 'Cold Soups', 'Broths', 'Cream Soups'],
            'Snacks' => ['Healthy Snacks', 'Fried Snacks', 'Baked Snacks', 'Sweet Snacks'],
            'Breakfast' => ['Continental', 'Traditional', 'Healthy Options', 'Quick Bites'],
        ];

        // Return subcategories for the category, or default ones if not found
        return $subcategoriesMap[$categoryName] ?? ['Specialty Items', 'Popular Choices', 'Chef\'s Recommendations'];
    }
};
