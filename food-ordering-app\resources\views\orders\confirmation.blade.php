@extends('layouts.app')

@section('title', 'Order Confirmation - Food Ordering App')

@section('content')
<div class="container-mobile py-6">
    <div class="max-w-4xl mx-auto">
        <!-- Success Message -->
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-6">
            <div class="flex items-center">
                <i class="fas fa-check-circle text-xl mr-3"></i>
                <div>
                    <h2 class="font-semibold">Order Placed Successfully!</h2>
                    <p class="text-sm">Thank you for your order. We'll prepare it with care.</p>
                </div>
            </div>
        </div>

        <!-- Order Summary -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center justify-between mb-6">
                <h1 class="text-2xl font-bold text-gray-800">Order Confirmation</h1>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Order Number</p>
                    <p class="text-lg font-semibold text-orange-600">{{ $order->order_number }}</p>
                </div>
            </div>

            <!-- Order Status -->
            <div class="mb-6">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">Status</span>
                    <span class="px-3 py-1 rounded-full text-sm font-medium
                        @if($order->status === 'pending') bg-yellow-100 text-yellow-800
                        @elseif($order->status === 'confirmed') bg-blue-100 text-blue-800
                        @elseif($order->status === 'preparing') bg-orange-100 text-orange-800
                        @elseif($order->status === 'ready') bg-purple-100 text-purple-800
                        @elseif($order->status === 'out_for_delivery') bg-indigo-100 text-indigo-800
                        @elseif($order->status === 'delivered') bg-green-100 text-green-800
                        @elseif($order->status === 'cancelled') bg-red-100 text-red-800
                        @else bg-gray-100 text-gray-800
                        @endif">
                        {{ ucfirst(str_replace('_', ' ', $order->status)) }}
                    </span>
                </div>
                
                @if($order->estimated_delivery_time)
                    <p class="text-sm text-gray-600">
                        <i class="fas fa-clock mr-1"></i>
                        Estimated {{ $order->order_type === 'delivery' ? 'delivery' : 'pickup' }} time: 
                        {{ $order->estimated_delivery_time->format('M j, Y \a\t g:i A') }}
                    </p>
                @endif
            </div>

            <!-- Customer Information -->
            <div class="grid md:grid-cols-2 gap-6 mb-6">
                <div>
                    <h3 class="font-semibold text-lg mb-3">Customer Information</h3>
                    <div class="space-y-2 text-sm">
                        <p><span class="font-medium">Name:</span> {{ $order->customer_name }}</p>
                        <p><span class="font-medium">Email:</span> {{ $order->customer_email }}</p>
                        <p><span class="font-medium">Phone:</span> {{ $order->customer_phone }}</p>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold text-lg mb-3">
                        @if($order->order_type === 'delivery')
                            Delivery Information
                        @else
                            Pickup Information
                        @endif
                    </h3>
                    <div class="space-y-2 text-sm">
                        <p><span class="font-medium">Type:</span> {{ ucfirst($order->order_type) }}</p>
                        @if($order->order_type === 'delivery')
                            <p><span class="font-medium">Address:</span> {{ $order->delivery_address }}</p>
                            <p><span class="font-medium">City:</span> {{ $order->delivery_city }}, {{ $order->delivery_postal_code }}</p>
                            @if($order->delivery_instructions)
                                <p><span class="font-medium">Instructions:</span> {{ $order->delivery_instructions }}</p>
                            @endif
                        @else
                            <p class="text-gray-600">Please pick up your order from our restaurant.</p>
                        @endif
                        <p><span class="font-medium">Payment:</span> {{ ucfirst(str_replace('_', ' ', $order->payment_method)) }}</p>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="mb-6">
                <h3 class="font-semibold text-lg mb-4">Order Items</h3>
                <div class="space-y-3">
                    @foreach($order->orderItems as $orderItem)
                        @php
                            $item = $orderItem->foodItem ?? $orderItem->package;
                        @endphp
                        <div class="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                            <!-- Item Image -->
                            <div class="flex-shrink-0">
                                @if($orderItem->item_image)
                                    <img src="{{ $orderItem->item_image }}" alt="{{ $orderItem->item_name }}" 
                                         class="w-16 h-16 rounded-lg object-cover">
                                @else
                                    <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-image text-gray-400"></i>
                                    </div>
                                @endif
                            </div>
                            
                            <!-- Item Details -->
                            <div class="flex-1">
                                <h4 class="font-medium">{{ $orderItem->item_name }}</h4>
                                @if($orderItem->item_description)
                                    <p class="text-sm text-gray-600">{{ $orderItem->item_description }}</p>
                                @endif
                                <p class="text-sm text-gray-600">
                                    Quantity: {{ $orderItem->quantity }} × ${{ number_format($orderItem->unit_price, 2) }}
                                </p>
                                @if($orderItem->special_instructions)
                                    <p class="text-sm text-orange-600 mt-1">
                                        <i class="fas fa-sticky-note"></i> {{ $orderItem->special_instructions }}
                                    </p>
                                @endif
                                @if($orderItem->customizations)
                                    <p class="text-sm text-blue-600 mt-1">
                                        <i class="fas fa-cog"></i> 
                                        @foreach($orderItem->customizations as $key => $value)
                                            {{ ucfirst($key) }}: {{ $value }}{{ !$loop->last ? ', ' : '' }}
                                        @endforeach
                                    </p>
                                @endif
                            </div>
                            
                            <!-- Item Total -->
                            <div class="text-right">
                                <span class="font-semibold">${{ number_format($orderItem->total_price, 2) }}</span>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- Order Total -->
            <div class="border-t border-gray-200 pt-4">
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span>Subtotal</span>
                        <span>${{ number_format($order->subtotal, 2) }}</span>
                    </div>
                    @if($order->tax_amount > 0)
                        <div class="flex justify-between text-sm text-gray-600">
                            <span>Tax</span>
                            <span>${{ number_format($order->tax_amount, 2) }}</span>
                        </div>
                    @endif
                    @if($order->delivery_fee > 0)
                        <div class="flex justify-between text-sm text-gray-600">
                            <span>Delivery Fee</span>
                            <span>${{ number_format($order->delivery_fee, 2) }}</span>
                        </div>
                    @endif
                    @if($order->discount_amount > 0)
                        <div class="flex justify-between text-sm text-green-600">
                            <span>Discount</span>
                            <span>-${{ number_format($order->discount_amount, 2) }}</span>
                        </div>
                    @endif
                    <hr class="my-2">
                    <div class="flex justify-between font-bold text-lg">
                        <span>Total</span>
                        <span class="text-orange-600">${{ number_format($order->total_amount, 2) }}</span>
                    </div>
                </div>
            </div>

            @if($order->notes)
                <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                    <h4 class="font-medium text-blue-800 mb-2">Order Notes</h4>
                    <p class="text-sm text-blue-700">{{ $order->notes }}</p>
                </div>
            @endif
        </div>

        <!-- Next Steps -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
            <h3 class="text-lg font-semibold text-blue-800 mb-3">What Happens Next?</h3>
            <div class="space-y-2 text-sm text-blue-700">
                <div class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">1</span>
                    <span>We'll confirm your order and start preparing your delicious food.</span>
                </div>
                <div class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">2</span>
                    <span>You'll receive updates about your order status via email and SMS.</span>
                </div>
                <div class="flex items-start">
                    <span class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">3</span>
                    <span>
                        @if($order->order_type === 'delivery')
                            Your order will be delivered to your specified address.
                        @else
                            Your order will be ready for pickup at our restaurant.
                        @endif
                    </span>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('orders.track', $order->order_number) }}" 
               class="bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-orange-700 transition-colors text-center">
                Track Your Order
            </a>
            <a href="{{ route('menu.index') }}" 
               class="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-200 transition-colors text-center">
                Order Again
            </a>
            <a href="{{ route('home') }}" 
               class="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-200 transition-colors text-center">
                Back to Home
            </a>
        </div>
    </div>
</div>
@endsection
