<?php

namespace App\Http\Controllers;

use App\Models\CartItem;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;

class OrderController extends Controller
{
    /**
     * Display the checkout page.
     */
    public function checkout()
    {
        $cartItems = CartItem::where('session_id', Session::getId())
            ->with(['foodItem.category', 'foodItem.cuisine', 'package.category', 'package.cuisine'])
            ->get();

        if ($cartItems->isEmpty()) {
            return redirect()->route('menu.index')->with('error', 'Your cart is empty.');
        }

        $subtotal = $cartItems->sum('total_price');
        $taxRate = 0.10; // 10% tax
        $taxAmount = $subtotal * $taxRate;
        $deliveryFee = $subtotal >= 50 ? 0 : 5.99; // Free delivery over $50
        $total = $subtotal + $taxAmount + $deliveryFee;

        return view('orders.checkout', compact(
            'cartItems',
            'subtotal',
            'taxAmount',
            'deliveryFee',
            'total'
        ));
    }

    /**
     * Place a new order.
     */
    public function placeOrder(Request $request)
    {
        $request->validate([
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email|max:255',
            'customer_phone' => 'required|string|max:20',
            'delivery_address' => 'required|string|max:500',
            'delivery_city' => 'required|string|max:100',
            'delivery_postal_code' => 'required|string|max:10',
            'delivery_instructions' => 'nullable|string|max:500',
            'order_type' => 'required|in:delivery,pickup',
            'payment_method' => 'required|in:cash,card,online,wallet',
        ]);

        $cartItems = CartItem::where('session_id', Session::getId())
            ->with(['foodItem', 'package'])
            ->get();

        if ($cartItems->isEmpty()) {
            return redirect()->route('menu.index')->with('error', 'Your cart is empty.');
        }

        DB::beginTransaction();

        try {
            // Calculate totals
            $subtotal = $cartItems->sum('total_price');
            $taxRate = 0.10;
            $taxAmount = $subtotal * $taxRate;
            $deliveryFee = $request->order_type === 'pickup' ? 0 : ($subtotal >= 50 ? 0 : 5.99);
            $total = $subtotal + $taxAmount + $deliveryFee;

            // Create order
            $order = Order::create([
                'order_number' => Order::generateOrderNumber(),
                'user_id' => auth()->id(),
                'customer_name' => $request->customer_name,
                'customer_email' => $request->customer_email,
                'customer_phone' => $request->customer_phone,
                'delivery_address' => $request->delivery_address,
                'delivery_city' => $request->delivery_city,
                'delivery_postal_code' => $request->delivery_postal_code,
                'delivery_instructions' => $request->delivery_instructions,
                'order_type' => $request->order_type,
                'payment_method' => $request->payment_method,
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'delivery_fee' => $deliveryFee,
                'total_amount' => $total,
                'estimated_delivery_time' => now()->addMinutes(45), // 45 minutes from now
                'status' => 'pending',
                'payment_status' => 'pending',
            ]);

            // Create order items
            foreach ($cartItems as $cartItem) {
                $item = $cartItem->foodItem ?? $cartItem->package;
                
                OrderItem::create([
                    'order_id' => $order->id,
                    'food_item_id' => $cartItem->food_item_id,
                    'package_id' => $cartItem->package_id,
                    'item_name' => $item->name,
                    'item_description' => $item->description,
                    'item_image' => $item->image,
                    'quantity' => $cartItem->quantity,
                    'unit_weight' => $cartItem->unit_weight,
                    'unit_price' => $cartItem->unit_price,
                    'total_price' => $cartItem->total_price,
                    'order_type' => $cartItem->order_type,
                    'customizations' => $cartItem->customizations,
                    'special_instructions' => $cartItem->special_instructions,
                ]);
            }

            // Clear cart
            CartItem::where('session_id', Session::getId())->delete();

            DB::commit();

            return redirect()->route('orders.confirmation', $order->order_number)
                ->with('success', 'Your order has been placed successfully!');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'There was an error placing your order. Please try again.');
        }
    }

    /**
     * Display order confirmation.
     */
    public function confirmation(Order $order)
    {
        $order->load(['orderItems.foodItem', 'orderItems.package']);
        
        return view('orders.confirmation', compact('order'));
    }

    /**
     * Track an order.
     */
    public function track(Order $order)
    {
        $order->load(['orderItems.foodItem', 'orderItems.package']);
        
        return view('orders.track', compact('order'));
    }
}
