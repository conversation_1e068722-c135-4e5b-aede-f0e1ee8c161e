@extends('admin.layouts.app')

@section('title', 'Food Items Management')
@section('page-title', 'Food Items Management')

@section('page-actions')
<a href="{{ route('admin.food-items.create') }}" class="btn btn-primary">
    <i class="bi bi-plus-circle me-1"></i>
    Add New Food Item
</a>
@endsection

@section('content')
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">All Food Items</h5>
            </div>
            <div class="col-auto">
                <!-- Search and Filter Form -->
                <form method="GET" action="{{ route('admin.food-items.index') }}" class="d-flex gap-2">
                    <input type="text" name="search" class="form-control form-control-sm" 
                           placeholder="Search food items..." value="{{ request('search') }}">
                    
                    <select name="category_id" class="form-select form-select-sm">
                        <option value="">All Categories</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                    
                    <select name="cuisine_id" class="form-select form-select-sm">
                        <option value="">All Cuisines</option>
                        @foreach($cuisines as $cuisine)
                            <option value="{{ $cuisine->id }}" {{ request('cuisine_id') == $cuisine->id ? 'selected' : '' }}>
                                {{ $cuisine->name }}
                            </option>
                        @endforeach
                    </select>
                    
                    <select name="availability" class="form-select form-select-sm">
                        <option value="">All Status</option>
                        <option value="available" {{ request('availability') === 'available' ? 'selected' : '' }}>Available</option>
                        <option value="unavailable" {{ request('availability') === 'unavailable' ? 'selected' : '' }}>Unavailable</option>
                    </select>
                    
                    <button type="submit" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-search"></i>
                    </button>
                    
                    @if(request()->hasAny(['search', 'category_id', 'cuisine_id', 'availability']))
                        <a href="{{ route('admin.food-items.index') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-x-circle"></i>
                        </a>
                    @endif
                </form>
            </div>
        </div>
    </div>
    
    <!-- Bulk Actions -->
    <div class="card-body border-bottom">
        <form id="bulkActionForm" method="POST" action="{{ route('admin.food-items.bulk-action') }}">
            @csrf
            <div class="row align-items-center">
                <div class="col-auto">
                    <input type="checkbox" id="selectAll" class="form-check-input">
                    <label for="selectAll" class="form-check-label">Select All</label>
                </div>
                <div class="col-auto">
                    <select name="action" class="form-select form-select-sm" required>
                        <option value="">Bulk Actions</option>
                        <option value="activate">Activate</option>
                        <option value="deactivate">Deactivate</option>
                        <option value="feature">Feature</option>
                        <option value="unfeature">Unfeature</option>
                        <option value="delete">Delete</option>
                    </select>
                </div>
                <div class="col-auto">
                    <button type="submit" class="btn btn-outline-primary btn-sm">Apply</button>
                </div>
            </div>
        </form>
    </div>
    
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th width="40">
                            <input type="checkbox" class="form-check-input" id="selectAllHeader">
                        </th>
                        <th>Image</th>
                        <th>Name</th>
                        <th>Category</th>
                        <th>Cuisine</th>
                        <th>Price</th>
                        <th>Status</th>
                        <th>Featured</th>
                        <th>Stock</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($foodItems as $item)
                        <tr>
                            <td>
                                <input type="checkbox" name="items[]" value="{{ $item->id }}" class="form-check-input item-checkbox">
                            </td>
                            <td>
                                @if($item->image)
                                    <img src="{{ Storage::url($item->image) }}" alt="{{ $item->name }}" 
                                         class="rounded" width="50" height="50" style="object-fit: cover;">
                                @else
                                    <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                         style="width: 50px; height: 50px;">
                                        <i class="bi bi-image text-muted"></i>
                                    </div>
                                @endif
                            </td>
                            <td>
                                <div>
                                    <div class="fw-bold">{{ $item->name }}</div>
                                    <small class="text-muted">{{ Str::limit($item->description, 50) }}</small>
                                </div>
                            </td>
                            <td>{{ $item->category->name ?? '-' }}</td>
                            <td>{{ $item->cuisine->name ?? '-' }}</td>
                            <td>
                                <div class="fw-bold">${{ number_format($item->price_per_unit, 2) }}</div>
                                <small class="text-muted">per {{ $item->unit }}</small>
                            </td>
                            <td>
                                <span class="badge bg-{{ $item->is_available ? 'success' : 'secondary' }}">
                                    {{ $item->is_available ? 'Available' : 'Unavailable' }}
                                </span>
                            </td>
                            <td>
                                @if($item->is_featured)
                                    <i class="bi bi-star-fill text-warning" title="Featured"></i>
                                @endif
                                @if($item->is_popular)
                                    <i class="bi bi-fire text-danger" title="Popular"></i>
                                @endif
                            </td>
                            <td>
                                @if($item->stock_quantity !== null)
                                    <span class="badge bg-{{ $item->stock_quantity > 10 ? 'success' : ($item->stock_quantity > 0 ? 'warning' : 'danger') }}">
                                        {{ $item->stock_quantity }}
                                    </span>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ route('admin.food-items.show', $item) }}" class="btn btn-outline-info" title="View">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.food-items.edit', $item) }}" class="btn btn-outline-primary" title="Edit">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    
                                    <form method="POST" action="{{ route('admin.food-items.toggle-availability', $item) }}" class="d-inline">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit" class="btn btn-outline-{{ $item->is_available ? 'warning' : 'success' }}" 
                                                title="{{ $item->is_available ? 'Mark Unavailable' : 'Mark Available' }}">
                                            <i class="bi bi-{{ $item->is_available ? 'pause' : 'play' }}"></i>
                                        </button>
                                    </form>
                                    
                                    <form method="POST" action="{{ route('admin.food-items.destroy', $item) }}" class="d-inline"
                                          onsubmit="return confirm('Are you sure you want to delete this food item?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-outline-danger" title="Delete">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="10" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-egg-fried fs-1 d-block mb-2"></i>
                                    No food items found
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
    
    @if($foodItems->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    Showing {{ $foodItems->firstItem() }} to {{ $foodItems->lastItem() }} of {{ $foodItems->total() }} results
                </div>
                {{ $foodItems->links() }}
            </div>
        </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
// Select All functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.item-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

document.getElementById('selectAllHeader').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.item-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Bulk action form submission
document.getElementById('bulkActionForm').addEventListener('submit', function(e) {
    const selectedItems = document.querySelectorAll('.item-checkbox:checked');
    if (selectedItems.length === 0) {
        e.preventDefault();
        alert('Please select at least one item.');
        return;
    }
    
    const action = document.querySelector('select[name="action"]').value;
    if (!action) {
        e.preventDefault();
        alert('Please select an action.');
        return;
    }
    
    if (action === 'delete') {
        if (!confirm('Are you sure you want to delete the selected items?')) {
            e.preventDefault();
        }
    }
});
</script>
@endpush
