<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('packages', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('image')->nullable();
            $table->json('images')->nullable();
            
            // Pricing
            $table->decimal('price', 10, 2);
            $table->decimal('original_price', 10, 2)->nullable(); // For showing discounts
            $table->decimal('discount_percentage', 5, 2)->default(0);
            
            // Package Details
            $table->enum('package_type', ['combo', 'family', 'party', 'special'])->default('combo');
            $table->integer('serves_people')->nullable(); // How many people this package serves
            
            // Categories and Cuisine
            $table->foreignId('category_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('cuisine_id')->nullable()->constrained()->onDelete('set null');
            
            // Dietary Information
            $table->boolean('is_vegetarian')->default(false);
            $table->boolean('is_vegan')->default(false);
            $table->boolean('is_gluten_free')->default(false);
            
            // Availability
            $table->boolean('is_available')->default(true);
            $table->boolean('is_popular')->default(false);
            $table->boolean('is_featured')->default(false);
            
            // Timing
            $table->integer('preparation_time')->nullable(); // in minutes
            $table->datetime('available_from')->nullable();
            $table->datetime('available_until')->nullable();
            
            // Ordering
            $table->integer('sort_order')->default(0);
            $table->integer('stock_quantity')->nullable();
            $table->integer('min_order_quantity')->default(1);
            $table->integer('max_order_quantity')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['is_vegetarian', 'is_available']);
            $table->index(['package_type', 'is_available']);
            $table->index(['is_popular', 'is_available']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('packages');
    }
};
