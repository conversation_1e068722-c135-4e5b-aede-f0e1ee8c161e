@extends('layouts.app')

@section('title', 'Catering Services - Food Ordering App')

@section('content')
<div class="container-mobile py-6">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-lg p-6 mb-8">
        <div class="text-center">
            <i class="fas fa-concierge-bell text-4xl mb-4"></i>
            <h1 class="text-3xl font-bold mb-2">Professional Catering Services</h1>
            <p class="text-lg mb-6">Make your events memorable with our delicious catering options</p>
            <a href="{{ route('catering.booking') }}" class="bg-white text-orange-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-block">
                Get Quote Now
            </a>
        </div>
    </div>

    <!-- Event Types -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Perfect for Any Occasion</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            @foreach($eventTypes as $eventType)
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="text-center">
                    <i class="{{ $eventType->icon ?? 'fas fa-calendar-alt' }} text-3xl text-orange-600 mb-3"></i>
                    <h3 class="text-lg font-semibold mb-2">{{ $eventType->name }}</h3>
                    <p class="text-gray-600 text-sm mb-4">{{ $eventType->description }}</p>
                    <div class="text-sm text-gray-500">
                        <p>{{ $eventType->min_guests }}-{{ $eventType->max_guests }} guests</p>
                        <p>From ${{ number_format($eventType->base_price_per_person, 2) }}/person</p>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>

    <!-- Popular Packages -->
    @if($popularPackages->count() > 0)
    <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Popular Catering Packages</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($popularPackages as $package)
            <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                @if($package->image_url)
                <img src="{{ $package->image_url }}" alt="{{ $package->name }}" class="w-full h-48 object-cover">
                @else
                <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                    <i class="fas fa-utensils text-4xl text-gray-400"></i>
                </div>
                @endif
                
                <div class="p-6">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="text-lg font-semibold">{{ $package->name }}</h3>
                        @if($package->is_popular)
                        <span class="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">Popular</span>
                        @endif
                    </div>
                    
                    <p class="text-gray-600 text-sm mb-4">{{ $package->short_description }}</p>
                    
                    <div class="mb-4">
                        <p class="text-lg font-bold text-orange-600">
                            From ${{ number_format($package->base_price_per_person, 2) }}/person
                        </p>
                        <p class="text-sm text-gray-500">{{ $package->min_guests }}-{{ $package->max_guests }} guests</p>
                    </div>
                    
                    @if($package->dietary_options)
                    <div class="mb-4">
                        <div class="flex flex-wrap gap-1">
                            @foreach($package->dietary_options as $option)
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">{{ $option }}</span>
                            @endforeach
                        </div>
                    </div>
                    @endif
                    
                    <div class="flex gap-2">
                        <a href="{{ route('catering.package', $package->slug) }}" class="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded text-center text-sm hover:bg-gray-200 transition-colors">
                            View Details
                        </a>
                        <a href="{{ route('catering.booking', ['package' => $package->id]) }}" class="flex-1 bg-orange-600 text-white px-4 py-2 rounded text-center text-sm hover:bg-orange-700 transition-colors">
                            Book Now
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif

    <!-- Features -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Why Choose Our Catering?</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="text-center">
                <i class="fas fa-clock text-3xl text-orange-600 mb-3"></i>
                <h3 class="font-semibold mb-2">On-Time Delivery</h3>
                <p class="text-gray-600 text-sm">We guarantee timely delivery for your events</p>
            </div>
            <div class="text-center">
                <i class="fas fa-users text-3xl text-orange-600 mb-3"></i>
                <h3 class="font-semibold mb-2">Professional Staff</h3>
                <p class="text-gray-600 text-sm">Experienced serving staff available</p>
            </div>
            <div class="text-center">
                <i class="fas fa-leaf text-3xl text-orange-600 mb-3"></i>
                <h3 class="font-semibold mb-2">Fresh Ingredients</h3>
                <p class="text-gray-600 text-sm">Only the freshest, highest quality ingredients</p>
            </div>
            <div class="text-center">
                <i class="fas fa-heart text-3xl text-orange-600 mb-3"></i>
                <h3 class="font-semibold mb-2">Dietary Options</h3>
                <p class="text-gray-600 text-sm">Vegetarian, vegan, and gluten-free options</p>
            </div>
        </div>
    </div>

    <!-- CTA Section -->
    <div class="bg-gray-100 rounded-lg p-6 text-center">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Ready to Plan Your Event?</h2>
        <p class="text-gray-600 mb-6">Get a personalized quote for your catering needs</p>
        <a href="{{ route('catering.booking') }}" class="bg-orange-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-700 transition-colors inline-block">
            Start Planning
        </a>
    </div>
</div>
@endsection
