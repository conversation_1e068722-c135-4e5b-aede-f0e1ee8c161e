<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create Super Admin
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'mobile' => '9999999999',
                'password' => Hash::make('password123'),
                'role' => 'super_admin',
                'is_active' => true,
                'email_verified_at' => now(),
                'mobile_verified_at' => now(),
            ]
        );

        // Create Regular Admin
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Restaurant Manager',
                'mobile' => '8888888888',
                'password' => Hash::make('password123'),
                'role' => 'admin',
                'is_active' => true,
                'email_verified_at' => now(),
                'mobile_verified_at' => now(),
            ]
        );

        // Create some test customers
        User::firstOrCreate(
            ['mobile' => '1234567890'],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+1234567890',
                'address' => '123 Main St, City, State 12345',
                'role' => 'customer',
                'is_active' => true,
                'email_verified_at' => now(),
                'mobile_verified_at' => now(),
            ]
        );

        User::firstOrCreate(
            ['mobile' => '0987654321'],
            [
                'name' => 'Jane Smith',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+1987654321',
                'address' => '456 Oak Ave, City, State 67890',
                'role' => 'customer',
                'is_active' => true,
                'email_verified_at' => now(),
                'mobile_verified_at' => now(),
            ]
        );
    }
}
