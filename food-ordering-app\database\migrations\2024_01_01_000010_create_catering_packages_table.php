<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('catering_packages', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->text('short_description')->nullable();
            $table->string('image_url')->nullable();
            $table->decimal('base_price_per_person', 8, 2);
            $table->integer('min_guests')->default(10);
            $table->integer('max_guests')->default(500);
            $table->json('included_items')->nullable(); // Array of included food items
            $table->json('dietary_options')->nullable(); // vegetarian, vegan, gluten-free, etc.
            $table->json('service_includes')->nullable(); // setup, cleanup, serving staff, etc.
            $table->integer('preparation_time_hours')->default(24);
            $table->boolean('is_popular')->default(false);
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('catering_packages');
    }
};
