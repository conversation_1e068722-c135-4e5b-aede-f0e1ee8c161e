@extends('admin.layouts.app')

@section('title', 'Orders Management')
@section('page-title', 'Orders Management')

@section('page-actions')
<div class="btn-group" role="group">
    <a href="{{ route('admin.orders.export') }}{{ request()->getQueryString() ? '?' . request()->getQueryString() : '' }}" 
       class="btn btn-outline-success">
        <i class="bi bi-download me-1"></i>
        Export
    </a>
    <a href="{{ route('admin.orders.statistics') }}" class="btn btn-outline-info">
        <i class="bi bi-bar-chart me-1"></i>
        Statistics
    </a>
</div>
@endsection

@section('content')
<!-- Status Filter Badges -->
<div class="row mb-3">
    <div class="col">
        <div class="d-flex gap-2 flex-wrap">
            <a href="{{ route('admin.orders.index') }}" 
               class="btn btn-outline-primary {{ !request('status') ? 'active' : '' }}">
                All Orders
                <span class="badge bg-primary ms-1">{{ array_sum($statusCounts->toArray()) }}</span>
            </a>
            @foreach(['pending', 'confirmed', 'preparing', 'ready', 'out_for_delivery', 'delivered', 'cancelled'] as $status)
                <a href="{{ route('admin.orders.index', ['status' => $status]) }}" 
                   class="btn btn-outline-{{ $status === 'delivered' ? 'success' : ($status === 'cancelled' ? 'danger' : 'warning') }} {{ request('status') === $status ? 'active' : '' }}">
                    {{ ucfirst(str_replace('_', ' ', $status)) }}
                    <span class="badge bg-{{ $status === 'delivered' ? 'success' : ($status === 'cancelled' ? 'danger' : 'warning') }} ms-1">
                        {{ $statusCounts[$status] ?? 0 }}
                    </span>
                </a>
            @endforeach
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">All Orders</h5>
            </div>
            <div class="col-auto">
                <!-- Search and Filter Form -->
                <form method="GET" action="{{ route('admin.orders.index') }}" class="d-flex gap-2">
                    <input type="text" name="search" class="form-control form-control-sm" 
                           placeholder="Search orders..." value="{{ request('search') }}">
                    
                    <select name="payment_status" class="form-select form-select-sm">
                        <option value="">All Payment Status</option>
                        <option value="pending" {{ request('payment_status') === 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="paid" {{ request('payment_status') === 'paid' ? 'selected' : '' }}>Paid</option>
                        <option value="failed" {{ request('payment_status') === 'failed' ? 'selected' : '' }}>Failed</option>
                        <option value="refunded" {{ request('payment_status') === 'refunded' ? 'selected' : '' }}>Refunded</option>
                    </select>
                    
                    <select name="order_type" class="form-select form-select-sm">
                        <option value="">All Types</option>
                        <option value="delivery" {{ request('order_type') === 'delivery' ? 'selected' : '' }}>Delivery</option>
                        <option value="pickup" {{ request('order_type') === 'pickup' ? 'selected' : '' }}>Pickup</option>
                        <option value="dine_in" {{ request('order_type') === 'dine_in' ? 'selected' : '' }}>Dine In</option>
                    </select>
                    
                    <input type="date" name="date_from" class="form-control form-control-sm" 
                           value="{{ request('date_from') }}" placeholder="From Date">
                    
                    <input type="date" name="date_to" class="form-control form-control-sm" 
                           value="{{ request('date_to') }}" placeholder="To Date">
                    
                    <button type="submit" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-search"></i>
                    </button>
                    
                    @if(request()->hasAny(['search', 'payment_status', 'order_type', 'date_from', 'date_to']))
                        <a href="{{ route('admin.orders.index') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-x-circle"></i>
                        </a>
                    @endif
                </form>
            </div>
        </div>
    </div>
    
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Order #</th>
                        <th>Customer</th>
                        <th>Items</th>
                        <th>Type</th>
                        <th>Amount</th>
                        <th>Payment</th>
                        <th>Status</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($orders as $order)
                        <tr>
                            <td>
                                <div class="fw-bold">{{ $order->order_number }}</div>
                                @if($order->notes)
                                    <small class="text-muted">
                                        <i class="bi bi-chat-left-text"></i>
                                        {{ Str::limit($order->notes, 30) }}
                                    </small>
                                @endif
                            </td>
                            <td>
                                <div>
                                    <div class="fw-bold">{{ $order->customer_name }}</div>
                                    <small class="text-muted">{{ $order->customer_email }}</small>
                                    @if($order->customer_phone)
                                        <br><small class="text-muted">{{ $order->customer_phone }}</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $order->orderItems->count() }} items</span>
                                @if($order->orderItems->count() > 0)
                                    <br><small class="text-muted">{{ $order->orderItems->first()->foodItem->name ?? 'N/A' }}
                                    @if($order->orderItems->count() > 1)
                                        +{{ $order->orderItems->count() - 1 }} more
                                    @endif
                                    </small>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $order->order_type)) }}</span>
                            </td>
                            <td>
                                <div class="fw-bold">${{ number_format($order->total_amount, 2) }}</div>
                                <small class="text-muted">
                                    Sub: ${{ number_format($order->subtotal, 2) }}
                                    @if($order->delivery_fee > 0)
                                        + Delivery: ${{ number_format($order->delivery_fee, 2) }}
                                    @endif
                                </small>
                            </td>
                            <td>
                                <span class="badge bg-{{ $order->payment_status === 'paid' ? 'success' : ($order->payment_status === 'failed' ? 'danger' : 'warning') }}">
                                    {{ ucfirst($order->payment_status) }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-{{ $order->status === 'delivered' ? 'success' : ($order->status === 'cancelled' ? 'danger' : 'warning') }}">
                                    {{ ucfirst(str_replace('_', ' ', $order->status)) }}
                                </span>
                                @if($order->estimated_delivery_time)
                                    <br><small class="text-muted">
                                        ETA: {{ $order->estimated_delivery_time->format('H:i') }}
                                    </small>
                                @endif
                            </td>
                            <td>
                                <div>{{ $order->created_at->format('M d, Y') }}</div>
                                <small class="text-muted">{{ $order->created_at->format('H:i') }}</small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ route('admin.orders.show', $order) }}" class="btn btn-outline-info" title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.orders.invoice', $order) }}" class="btn btn-outline-secondary" title="Print Invoice" target="_blank">
                                        <i class="bi bi-printer"></i>
                                    </a>
                                    @if(!in_array($order->status, ['delivered', 'cancelled']))
                                        <button type="button" class="btn btn-outline-primary" title="Update Status" 
                                                data-bs-toggle="modal" data-bs-target="#statusModal{{ $order->id }}">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>

                        <!-- Status Update Modal -->
                        <div class="modal fade" id="statusModal{{ $order->id }}" tabindex="-1">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <form method="POST" action="{{ route('admin.orders.update-status', $order) }}">
                                        @csrf
                                        @method('PATCH')
                                        <div class="modal-header">
                                            <h5 class="modal-title">Update Order Status</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="mb-3">
                                                <label class="form-label">Order Number</label>
                                                <input type="text" class="form-control" value="{{ $order->order_number }}" readonly>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Current Status</label>
                                                <input type="text" class="form-control" value="{{ ucfirst(str_replace('_', ' ', $order->status)) }}" readonly>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">New Status</label>
                                                <select name="status" class="form-select" required>
                                                    <option value="pending" {{ $order->status === 'pending' ? 'selected' : '' }}>Pending</option>
                                                    <option value="confirmed" {{ $order->status === 'confirmed' ? 'selected' : '' }}>Confirmed</option>
                                                    <option value="preparing" {{ $order->status === 'preparing' ? 'selected' : '' }}>Preparing</option>
                                                    <option value="ready" {{ $order->status === 'ready' ? 'selected' : '' }}>Ready</option>
                                                    <option value="out_for_delivery" {{ $order->status === 'out_for_delivery' ? 'selected' : '' }}>Out for Delivery</option>
                                                    <option value="delivered" {{ $order->status === 'delivered' ? 'selected' : '' }}>Delivered</option>
                                                    <option value="cancelled" {{ $order->status === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Notes (Optional)</label>
                                                <textarea name="notes" class="form-control" rows="3" placeholder="Add any notes about this status update..."></textarea>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                            <button type="submit" class="btn btn-primary">Update Status</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    @empty
                        <tr>
                            <td colspan="9" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-receipt fs-1 d-block mb-2"></i>
                                    No orders found
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
    
    @if($orders->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    Showing {{ $orders->firstItem() }} to {{ $orders->lastItem() }} of {{ $orders->total() }} results
                </div>
                {{ $orders->links() }}
            </div>
        </div>
    @endif
</div>
@endsection
