<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\MenuController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\CateringController;
use App\Http\Controllers\CateringOrderController;

// Admin Controllers
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\UserController as AdminUserController;
use App\Http\Controllers\Admin\FoodItemController as AdminFoodItemController;
use App\Http\Controllers\Admin\OrderController as AdminOrderController;
use App\Http\Controllers\Admin\CategoryController as AdminCategoryController;
use App\Http\Controllers\Admin\CuisineController as AdminCuisineController;
use App\Http\Controllers\Admin\Auth\AdminLoginController;

// Auth Controllers
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;

// Authentication routes
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login']);
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

// Registration routes
Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
Route::post('/register', [RegisterController::class, 'register']);

// AJAX validation routes
Route::post('/check-mobile', [RegisterController::class, 'checkMobile'])->name('check.mobile');
Route::post('/check-email', [RegisterController::class, 'checkEmail'])->name('check.email');

// Home routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/search', [HomeController::class, 'search'])->name('search');

// Menu routes
Route::prefix('menu')->name('menu.')->group(function () {
    Route::get('/', [MenuController::class, 'index'])->name('index');
    Route::get('/food-item/{foodItem:slug}', [MenuController::class, 'showFoodItem'])->name('food-item');
    Route::get('/package/{package:slug}', [MenuController::class, 'showPackage'])->name('package');
    Route::get('/category/{category:slug}', [MenuController::class, 'getByCategory'])->name('category');
    Route::get('/cuisine/{cuisine:slug}', [MenuController::class, 'getByCuisine'])->name('cuisine');
});

// Cart routes
Route::prefix('cart')->name('cart.')->group(function () {
    Route::get('/', [CartController::class, 'index'])->name('index');
    Route::post('/add', [CartController::class, 'addItem'])->name('add');
    Route::patch('/update/{cartItem}', [CartController::class, 'updateItem'])->name('update');
    Route::delete('/remove/{cartItem}', [CartController::class, 'removeItem'])->name('remove');
    Route::delete('/clear', [CartController::class, 'clearCart'])->name('clear');
    Route::get('/count', [CartController::class, 'getCartCount'])->name('count');
});

// Order routes
Route::prefix('orders')->name('orders.')->group(function () {
    Route::get('/checkout', [OrderController::class, 'checkout'])->name('checkout');
    Route::post('/place', [OrderController::class, 'placeOrder'])->name('place');
    Route::get('/confirmation/{order:order_number}', [OrderController::class, 'confirmation'])->name('confirmation');
    Route::get('/track/{order:order_number}', [OrderController::class, 'track'])->name('track');
});

// Catering routes
Route::prefix('catering')->name('catering.')->group(function () {
    Route::get('/', [CateringController::class, 'index'])->name('index');
    Route::get('/booking', [CateringController::class, 'booking'])->name('booking');
    Route::get('/packages/{package:slug}', [CateringController::class, 'showPackage'])->name('package');

    // AJAX endpoints
    Route::get('/api/packages', [CateringController::class, 'getAvailablePackages'])->name('api.packages');
    Route::post('/api/pricing', [CateringController::class, 'calculatePricing'])->name('api.pricing');
    Route::post('/api/availability', [CateringController::class, 'checkAvailability'])->name('api.availability');

    // Order management
    Route::post('/order', [CateringOrderController::class, 'store'])->name('order.store');
    Route::get('/confirmation/{cateringOrder:order_number}', [CateringOrderController::class, 'confirmation'])->name('confirmation');
    Route::get('/track/{cateringOrder:order_number}', [CateringOrderController::class, 'track'])->name('track');
});

// Admin Authentication routes (outside middleware protection)
Route::prefix('admin')->name('admin.')->group(function () {
    Route::get('/login', [AdminLoginController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AdminLoginController::class, 'login']);
    Route::post('/logout', [AdminLoginController::class, 'logout'])->name('logout');
});

// Protected Admin routes
Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
    // Dashboard
    Route::get('/', [AdminController::class, 'index'])->name('dashboard');

    // User Management
    Route::resource('users', AdminUserController::class);
    Route::patch('users/{user}/toggle-status', [AdminUserController::class, 'toggleStatus'])->name('users.toggle-status');

    // Food Item Management
    Route::resource('food-items', AdminFoodItemController::class);
    Route::patch('food-items/{foodItem}/toggle-availability', [AdminFoodItemController::class, 'toggleAvailability'])->name('food-items.toggle-availability');
    Route::post('food-items/bulk-action', [AdminFoodItemController::class, 'bulkAction'])->name('food-items.bulk-action');

    // Order Management
    Route::resource('orders', AdminOrderController::class)->only(['index', 'show']);
    Route::patch('orders/{order}/status', [AdminOrderController::class, 'updateStatus'])->name('orders.update-status');
    Route::patch('orders/{order}/payment-status', [AdminOrderController::class, 'updatePaymentStatus'])->name('orders.update-payment-status');
    Route::get('orders/{order}/invoice', [AdminOrderController::class, 'printInvoice'])->name('orders.invoice');
    Route::get('orders-export', [AdminOrderController::class, 'export'])->name('orders.export');
    Route::get('orders-statistics', [AdminOrderController::class, 'statistics'])->name('orders.statistics');

    // Category Management
    Route::resource('categories', AdminCategoryController::class);
    Route::patch('categories/{category}/toggle-status', [AdminCategoryController::class, 'toggleStatus'])->name('categories.toggle-status');
    Route::post('categories/bulk-action', [AdminCategoryController::class, 'bulkAction'])->name('categories.bulk-action');

    // Cuisine Management
    Route::resource('cuisines', AdminCuisineController::class);
    Route::patch('cuisines/{cuisine}/toggle-status', [AdminCuisineController::class, 'toggleStatus'])->name('cuisines.toggle-status');
    Route::post('cuisines/bulk-action', [AdminCuisineController::class, 'bulkAction'])->name('cuisines.bulk-action');
});
