<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Appetizers',
                'slug' => 'appetizers',
                'description' => 'Start your meal with our delicious appetizers',
                'sort_order' => 1,
            ],
            [
                'name' => 'Main Course',
                'slug' => 'main-course',
                'description' => 'Hearty and satisfying main dishes',
                'sort_order' => 2,
            ],
            [
                'name' => 'Desserts',
                'slug' => 'desserts',
                'description' => 'Sweet treats to end your meal perfectly',
                'sort_order' => 3,
            ],
            [
                'name' => 'Beverages',
                'slug' => 'beverages',
                'description' => 'Refreshing drinks and beverages',
                'sort_order' => 4,
            ],
            [
                'name' => 'Soups',
                'slug' => 'soups',
                'description' => 'Warm and comforting soups',
                'sort_order' => 5,
            ],
            [
                'name' => 'Salads',
                'slug' => 'salads',
                'description' => 'Fresh and healthy salads',
                'sort_order' => 6,
            ],
            [
                'name' => 'Pizza',
                'slug' => 'pizza',
                'description' => 'Delicious pizzas with various toppings',
                'sort_order' => 7,
            ],
            [
                'name' => 'Pasta',
                'slug' => 'pasta',
                'description' => 'Classic pasta dishes',
                'sort_order' => 8,
            ],
            [
                'name' => 'Burgers',
                'slug' => 'burgers',
                'description' => 'Juicy burgers and sandwiches',
                'sort_order' => 9,
            ],
            [
                'name' => 'Rice Dishes',
                'slug' => 'rice-dishes',
                'description' => 'Flavorful rice-based meals',
                'sort_order' => 10,
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
