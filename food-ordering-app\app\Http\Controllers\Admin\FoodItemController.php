<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\FoodItem;
use App\Models\Category;
use App\Models\Cuisine;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FoodItemController extends Controller
{
    /**
     * Display a listing of food items.
     */
    public function index(Request $request)
    {
        $query = FoodItem::with(['category', 'cuisine']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('ingredients', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // Filter by cuisine
        if ($request->filled('cuisine_id')) {
            $query->where('cuisine_id', $request->cuisine_id);
        }

        // Filter by availability
        if ($request->filled('availability')) {
            $query->where('is_available', $request->availability === 'available');
        }

        $foodItems = $query->latest()->paginate(15);
        $categories = Category::active()->ordered()->get();
        $cuisines = Cuisine::active()->ordered()->get();

        return view('admin.food-items.index', compact('foodItems', 'categories', 'cuisines'));
    }

    /**
     * Show the form for creating a new food item.
     */
    public function create()
    {
        $categories = Category::active()->ordered()->get();
        $cuisines = Cuisine::active()->ordered()->get();
        return view('admin.food-items.create', compact('categories', 'cuisines'));
    }

    /**
     * Store a newly created food item in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'ingredients' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'price_per_unit' => 'required|numeric|min:0',
            'price_per_kg' => 'nullable|numeric|min:0',
            'unit' => 'required|string|max:50',
            'category_id' => 'required|exists:categories,id',
            'cuisine_id' => 'required|exists:cuisines,id',
            'is_vegetarian' => 'boolean',
            'is_vegan' => 'boolean',
            'is_gluten_free' => 'boolean',
            'is_spicy' => 'boolean',
            'spice_level' => 'nullable|integer|min:1|max:5',
            'is_available' => 'boolean',
            'is_popular' => 'boolean',
            'is_featured' => 'boolean',
            'allow_bulk_order' => 'boolean',
            'calories_per_unit' => 'nullable|integer|min:0',
            'protein' => 'nullable|numeric|min:0',
            'carbs' => 'nullable|numeric|min:0',
            'fat' => 'nullable|numeric|min:0',
            'preparation_time' => 'nullable|integer|min:0',
            'preparation_notes' => 'nullable|string',
            'stock_quantity' => 'nullable|integer|min:0',
            'min_order_quantity' => 'nullable|integer|min:1',
            'max_order_quantity' => 'nullable|integer|min:1',
        ]);

        // Generate slug
        $validated['slug'] = Str::slug($validated['name']);

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('food-items', 'public');
        }

        // Handle boolean fields
        $booleanFields = ['is_vegetarian', 'is_vegan', 'is_gluten_free', 'is_spicy', 'is_available', 'is_popular', 'is_featured', 'allow_bulk_order'];
        foreach ($booleanFields as $field) {
            $validated[$field] = $request->has($field);
        }

        FoodItem::create($validated);

        return redirect()->route('admin.food-items.index')
            ->with('success', 'Food item created successfully.');
    }

    /**
     * Display the specified food item.
     */
    public function show(FoodItem $foodItem)
    {
        $foodItem->load(['category', 'cuisine', 'orderItems']);
        return view('admin.food-items.show', compact('foodItem'));
    }

    /**
     * Show the form for editing the specified food item.
     */
    public function edit(FoodItem $foodItem)
    {
        $categories = Category::active()->ordered()->get();
        $cuisines = Cuisine::active()->ordered()->get();
        return view('admin.food-items.edit', compact('foodItem', 'categories', 'cuisines'));
    }

    /**
     * Update the specified food item in storage.
     */
    public function update(Request $request, FoodItem $foodItem)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'ingredients' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'price_per_unit' => 'required|numeric|min:0',
            'price_per_kg' => 'nullable|numeric|min:0',
            'unit' => 'required|string|max:50',
            'category_id' => 'required|exists:categories,id',
            'cuisine_id' => 'required|exists:cuisines,id',
            'is_vegetarian' => 'boolean',
            'is_vegan' => 'boolean',
            'is_gluten_free' => 'boolean',
            'is_spicy' => 'boolean',
            'spice_level' => 'nullable|integer|min:1|max:5',
            'is_available' => 'boolean',
            'is_popular' => 'boolean',
            'is_featured' => 'boolean',
            'allow_bulk_order' => 'boolean',
            'calories_per_unit' => 'nullable|integer|min:0',
            'protein' => 'nullable|numeric|min:0',
            'carbs' => 'nullable|numeric|min:0',
            'fat' => 'nullable|numeric|min:0',
            'preparation_time' => 'nullable|integer|min:0',
            'preparation_notes' => 'nullable|string',
            'stock_quantity' => 'nullable|integer|min:0',
            'min_order_quantity' => 'nullable|integer|min:1',
            'max_order_quantity' => 'nullable|integer|min:1',
        ]);

        // Update slug if name changed
        if ($validated['name'] !== $foodItem->name) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($foodItem->image) {
                Storage::disk('public')->delete($foodItem->image);
            }
            $validated['image'] = $request->file('image')->store('food-items', 'public');
        }

        // Handle boolean fields
        $booleanFields = ['is_vegetarian', 'is_vegan', 'is_gluten_free', 'is_spicy', 'is_available', 'is_popular', 'is_featured', 'allow_bulk_order'];
        foreach ($booleanFields as $field) {
            $validated[$field] = $request->has($field);
        }

        $foodItem->update($validated);

        return redirect()->route('admin.food-items.index')
            ->with('success', 'Food item updated successfully.');
    }

    /**
     * Remove the specified food item from storage.
     */
    public function destroy(FoodItem $foodItem)
    {
        // Delete image
        if ($foodItem->image) {
            Storage::disk('public')->delete($foodItem->image);
        }

        $foodItem->delete();

        return redirect()->route('admin.food-items.index')
            ->with('success', 'Food item deleted successfully.');
    }

    /**
     * Toggle food item availability.
     */
    public function toggleAvailability(FoodItem $foodItem)
    {
        $foodItem->update(['is_available' => !$foodItem->is_available]);

        $status = $foodItem->is_available ? 'available' : 'unavailable';
        return redirect()->back()
            ->with('success', "Food item marked as {$status}.");
    }

    /**
     * Bulk actions for food items.
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:delete,activate,deactivate,feature,unfeature',
            'items' => 'required|array',
            'items.*' => 'exists:food_items,id',
        ]);

        $foodItems = FoodItem::whereIn('id', $request->items);

        switch ($request->action) {
            case 'delete':
                $foodItems->each(function ($item) {
                    if ($item->image) {
                        Storage::disk('public')->delete($item->image);
                    }
                });
                $foodItems->delete();
                $message = 'Selected food items deleted successfully.';
                break;
            case 'activate':
                $foodItems->update(['is_available' => true]);
                $message = 'Selected food items activated successfully.';
                break;
            case 'deactivate':
                $foodItems->update(['is_available' => false]);
                $message = 'Selected food items deactivated successfully.';
                break;
            case 'feature':
                $foodItems->update(['is_featured' => true]);
                $message = 'Selected food items featured successfully.';
                break;
            case 'unfeature':
                $foodItems->update(['is_featured' => false]);
                $message = 'Selected food items unfeatured successfully.';
                break;
        }

        return redirect()->back()->with('success', $message);
    }
}
