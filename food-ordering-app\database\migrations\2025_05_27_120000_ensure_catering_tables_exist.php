<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ensure catering_event_types table exists
        if (!Schema::hasTable('catering_event_types')) {
            Schema::create('catering_event_types', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('slug')->unique();
                $table->text('description')->nullable();
                $table->string('icon')->nullable(); // Font Awesome icon class
                $table->decimal('base_price_per_person', 8, 2)->default(0);
                $table->integer('min_guests')->default(10);
                $table->integer('max_guests')->default(500);
                $table->integer('advance_booking_hours')->default(48); // Minimum hours in advance
                $table->json('available_times')->nullable(); // Available time slots
                $table->boolean('is_active')->default(true);
                $table->integer('sort_order')->default(0);
                $table->timestamps();
            });
        }

        // Ensure catering_packages table exists
        if (!Schema::hasTable('catering_packages')) {
            Schema::create('catering_packages', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('slug')->unique();
                $table->text('description')->nullable();
                $table->text('short_description')->nullable();
                $table->string('image_url')->nullable();
                $table->decimal('base_price_per_person', 8, 2);
                $table->integer('min_guests')->default(10);
                $table->integer('max_guests')->default(500);
                $table->json('included_items')->nullable(); // Array of included food items
                $table->json('dietary_options')->nullable(); // vegetarian, vegan, gluten-free, etc.
                $table->json('service_includes')->nullable(); // setup, cleanup, serving staff, etc.
                $table->integer('preparation_time_hours')->default(24);
                $table->boolean('is_popular')->default(false);
                $table->boolean('is_active')->default(true);
                $table->integer('sort_order')->default(0);
                $table->timestamps();
            });
        }

        // Ensure catering_pricing_tiers table exists
        if (!Schema::hasTable('catering_pricing_tiers')) {
            Schema::create('catering_pricing_tiers', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('catering_package_id');
                $table->integer('min_guests');
                $table->integer('max_guests');
                $table->decimal('price_per_person', 8, 2);
                $table->decimal('setup_fee', 8, 2)->default(0);
                $table->decimal('service_fee', 8, 2)->default(0);
                $table->decimal('delivery_fee', 8, 2)->default(0);
                $table->text('tier_description')->nullable();
                $table->timestamps();

                $table->index(['catering_package_id', 'min_guests', 'max_guests'], 'catering_pricing_tiers_package_guests_idx');
            });
        }

        // Ensure catering_orders table exists
        if (!Schema::hasTable('catering_orders')) {
            Schema::create('catering_orders', function (Blueprint $table) {
                $table->id();
                $table->string('order_number')->unique();
                $table->unsignedBigInteger('user_id')->nullable();
                $table->unsignedBigInteger('catering_event_type_id');
                $table->unsignedBigInteger('catering_package_id');

                // Customer Information
                $table->string('customer_name');
                $table->string('customer_email');
                $table->string('customer_phone');
                $table->string('company_name')->nullable();

                // Event Details
                $table->string('event_name')->nullable();
                $table->date('event_date');
                $table->time('event_start_time');
                $table->time('event_end_time')->nullable();
                $table->integer('guest_count');
                $table->text('event_description')->nullable();

                // Location
                $table->text('event_address');
                $table->string('event_city');
                $table->string('event_state')->nullable();
                $table->string('event_postal_code');
                $table->text('venue_details')->nullable(); // indoor/outdoor, kitchen access, etc.

                // Catering Specifics
                $table->json('dietary_requirements')->nullable(); // allergies, restrictions
                $table->json('special_requests')->nullable();
                $table->boolean('needs_setup_service')->default(false);
                $table->boolean('needs_cleanup_service')->default(false);
                $table->boolean('needs_serving_staff')->default(false);
                $table->integer('serving_staff_count')->default(0);

                // Pricing
                $table->decimal('base_amount', 10, 2);
                $table->decimal('setup_fee', 8, 2)->default(0);
                $table->decimal('service_fee', 8, 2)->default(0);
                $table->decimal('delivery_fee', 8, 2)->default(0);
                $table->decimal('staff_fee', 8, 2)->default(0);
                $table->decimal('tax_amount', 8, 2)->default(0);
                $table->decimal('discount_amount', 8, 2)->default(0);
                $table->decimal('total_amount', 10, 2);

                // Order Management
                $table->enum('status', [
                    'pending', 'confirmed', 'in_preparation', 'ready',
                    'in_transit', 'delivered', 'completed', 'cancelled'
                ])->default('pending');
                $table->enum('payment_status', [
                    'pending', 'partial', 'paid', 'refunded'
                ])->default('pending');
                $table->string('payment_method')->nullable();

                // Timestamps
                $table->timestamp('confirmed_at')->nullable();
                $table->timestamp('preparation_started_at')->nullable();
                $table->timestamp('ready_at')->nullable();
                $table->timestamp('delivered_at')->nullable();
                $table->timestamp('completed_at')->nullable();
                $table->timestamp('cancelled_at')->nullable();

                $table->text('notes')->nullable();
                $table->text('cancellation_reason')->nullable();
                $table->json('status_history')->nullable();

                $table->timestamps();

                $table->index(['event_date', 'status'], 'catering_orders_date_status_idx');
                $table->index(['customer_email', 'event_date'], 'catering_orders_email_date_idx');
            });
        }

        // Ensure catering_order_items table exists
        if (!Schema::hasTable('catering_order_items')) {
            Schema::create('catering_order_items', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('catering_order_id');
                $table->string('item_type'); // 'food_item', 'package_item', 'custom_item'
                $table->unsignedBigInteger('item_id')->nullable(); // Reference to food_items or other tables
                $table->string('item_name'); // Store name for historical purposes
                $table->text('item_description')->nullable();
                $table->integer('quantity');
                $table->decimal('unit_price', 8, 2);
                $table->decimal('total_price', 10, 2);
                $table->json('customizations')->nullable(); // Special preparations, modifications
                $table->text('special_instructions')->nullable();
                $table->timestamps();

                $table->index(['catering_order_id', 'item_type'], 'catering_order_items_order_type_idx');
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop tables in reverse order to handle foreign key constraints
        Schema::dropIfExists('catering_order_items');
        Schema::dropIfExists('catering_orders');
        Schema::dropIfExists('catering_pricing_tiers');
        Schema::dropIfExists('catering_packages');
        Schema::dropIfExists('catering_event_types');
    }
};
