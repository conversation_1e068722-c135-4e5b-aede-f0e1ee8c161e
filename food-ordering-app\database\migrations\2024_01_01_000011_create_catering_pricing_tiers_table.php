<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('catering_pricing_tiers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('catering_package_id');
            $table->integer('min_guests');
            $table->integer('max_guests');
            $table->decimal('price_per_person', 8, 2);
            $table->decimal('setup_fee', 8, 2)->default(0);
            $table->decimal('service_fee', 8, 2)->default(0);
            $table->decimal('delivery_fee', 8, 2)->default(0);
            $table->text('tier_description')->nullable();
            $table->timestamps();

            $table->index(['catering_package_id', 'min_guests', 'max_guests'], 'catering_pricing_tiers_package_guests_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('catering_pricing_tiers');
    }
};
