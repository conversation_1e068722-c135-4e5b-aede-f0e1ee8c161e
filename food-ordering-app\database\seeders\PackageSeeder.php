<?php

namespace Database\Seeders;

use App\Models\Package;
use App\Models\FoodItem;
use App\Models\Category;
use App\Models\Cuisine;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class PackageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = Category::all();
        $cuisines = Cuisine::all();
        $foodItems = FoodItem::all();

        $packages = [
            [
                'name' => 'Indian Family Feast',
                'slug' => 'indian-family-feast',
                'description' => 'A complete Indian meal for the whole family including butter chicken, paneer tikka masala, rice, and naan',
                'price' => 45.99,
                'original_price' => 55.99,
                'discount_percentage' => 18,
                'package_type' => 'family',
                'serves_people' => 4,
                'category' => 'Main Course',
                'cuisine' => 'Indian',
                'is_vegetarian' => false,
                'is_popular' => true,
                'is_featured' => true,
                'preparation_time' => 35,
                'food_items' => [
                    ['name' => 'Butter Chicken', 'quantity' => 2],
                    ['name' => 'Paneer Tikka Masala', 'quantity' => 1],
                    ['name' => 'Vegetable Fried Rice', 'quantity' => 2],
                ]
            ],
            [
                'name' => 'Italian Combo',
                'slug' => 'italian-combo',
                'description' => 'Perfect Italian combination with pizza, pasta, and dessert',
                'price' => 32.99,
                'original_price' => 38.99,
                'discount_percentage' => 15,
                'package_type' => 'combo',
                'serves_people' => 2,
                'category' => 'Main Course',
                'cuisine' => 'Italian',
                'is_vegetarian' => true,
                'is_popular' => true,
                'preparation_time' => 25,
                'food_items' => [
                    ['name' => 'Margherita Pizza', 'quantity' => 1],
                    ['name' => 'Spaghetti Carbonara', 'quantity' => 1],
                    ['name' => 'Tiramisu', 'quantity' => 2],
                ]
            ],
            [
                'name' => 'American Burger Meal',
                'slug' => 'american-burger-meal',
                'description' => 'Classic American meal with burger, wings, and drink',
                'price' => 24.99,
                'package_type' => 'combo',
                'serves_people' => 1,
                'category' => 'Burgers',
                'cuisine' => 'American',
                'is_vegetarian' => false,
                'is_popular' => true,
                'preparation_time' => 20,
                'food_items' => [
                    ['name' => 'Classic Cheeseburger', 'quantity' => 1],
                    ['name' => 'Chicken Wings', 'quantity' => 1],
                    ['name' => 'Fresh Orange Juice', 'quantity' => 1],
                ]
            ],
            [
                'name' => 'Vegetarian Delight',
                'slug' => 'vegetarian-delight',
                'description' => 'A healthy vegetarian package with variety of dishes',
                'price' => 28.99,
                'package_type' => 'special',
                'serves_people' => 2,
                'category' => 'Main Course',
                'cuisine' => 'Indian',
                'is_vegetarian' => true,
                'is_vegan' => false,
                'is_featured' => true,
                'preparation_time' => 30,
                'food_items' => [
                    ['name' => 'Paneer Tikka Masala', 'quantity' => 2],
                    ['name' => 'Vegetable Spring Rolls', 'quantity' => 4],
                    ['name' => 'Mango Lassi', 'quantity' => 2],
                ]
            ],
            [
                'name' => 'Party Platter',
                'slug' => 'party-platter',
                'description' => 'Perfect for parties with a mix of appetizers and main courses',
                'price' => 89.99,
                'original_price' => 105.99,
                'discount_percentage' => 15,
                'package_type' => 'party',
                'serves_people' => 8,
                'category' => 'Main Course',
                'cuisine' => 'American',
                'is_vegetarian' => false,
                'is_popular' => true,
                'preparation_time' => 45,
                'food_items' => [
                    ['name' => 'Chicken Wings', 'quantity' => 4],
                    ['name' => 'Vegetable Spring Rolls', 'quantity' => 8],
                    ['name' => 'Classic Cheeseburger', 'quantity' => 4],
                    ['name' => 'Margherita Pizza', 'quantity' => 2],
                ]
            ],
            [
                'name' => 'Sweet Treats Box',
                'slug' => 'sweet-treats-box',
                'description' => 'Assorted desserts perfect for sharing',
                'price' => 19.99,
                'package_type' => 'special',
                'serves_people' => 4,
                'category' => 'Desserts',
                'cuisine' => 'American',
                'is_vegetarian' => true,
                'preparation_time' => 10,
                'food_items' => [
                    ['name' => 'Chocolate Brownie', 'quantity' => 2],
                    ['name' => 'Tiramisu', 'quantity' => 2],
                ]
            ],
        ];

        foreach ($packages as $packageData) {
            $category = $categories->where('name', $packageData['category'])->first();
            $cuisine = $cuisines->where('name', $packageData['cuisine'])->first();
            
            if ($category && $cuisine) {
                $package = Package::create([
                    'name' => $packageData['name'],
                    'slug' => $packageData['slug'],
                    'description' => $packageData['description'],
                    'price' => $packageData['price'],
                    'original_price' => $packageData['original_price'] ?? null,
                    'discount_percentage' => $packageData['discount_percentage'] ?? 0,
                    'package_type' => $packageData['package_type'],
                    'serves_people' => $packageData['serves_people'] ?? null,
                    'category_id' => $category->id,
                    'cuisine_id' => $cuisine->id,
                    'is_vegetarian' => $packageData['is_vegetarian'] ?? false,
                    'is_vegan' => $packageData['is_vegan'] ?? false,
                    'is_popular' => $packageData['is_popular'] ?? false,
                    'is_featured' => $packageData['is_featured'] ?? false,
                    'preparation_time' => $packageData['preparation_time'] ?? null,
                    'is_available' => true,
                ]);

                // Attach food items to the package
                if (isset($packageData['food_items'])) {
                    foreach ($packageData['food_items'] as $index => $itemData) {
                        $foodItem = $foodItems->where('name', $itemData['name'])->first();
                        if ($foodItem) {
                            $package->foodItems()->attach($foodItem->id, [
                                'quantity' => $itemData['quantity'],
                                'sort_order' => $index + 1,
                                'is_optional' => false,
                                'is_customizable' => false,
                            ]);
                        }
                    }
                }
            }
        }
    }
}
