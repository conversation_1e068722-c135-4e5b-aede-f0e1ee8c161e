<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('package_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('package_id')->constrained()->onDelete('cascade');
            $table->foreignId('food_item_id')->constrained()->onDelete('cascade');
            $table->integer('quantity')->default(1);
            $table->decimal('unit_weight', 8, 3)->nullable(); // For bulk items in kg
            $table->text('notes')->nullable(); // Special instructions for this item in package
            $table->boolean('is_optional')->default(false); // Can customer remove this item?
            $table->boolean('is_customizable')->default(false); // Can customer modify quantity?
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            // Ensure unique combination
            $table->unique(['package_id', 'food_item_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('package_items');
    }
};
