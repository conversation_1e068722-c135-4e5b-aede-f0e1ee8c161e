@extends('layouts.app')

@section('title', 'Book Catering Service - Food Ordering App')

@section('content')
<div class="container-mobile py-6">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Book Your Catering Service</h1>
            <p class="text-gray-600">Fill out the form below to get a personalized quote</p>
        </div>

        <!-- Progress Steps -->
        <div class="mb-8">
            <div class="flex items-center justify-center space-x-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">1</div>
                    <span class="ml-2 text-sm font-medium text-orange-600">Event Details</span>
                </div>
                <div class="w-8 h-0.5 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-semibold">2</div>
                    <span class="ml-2 text-sm font-medium text-gray-600">Package Selection</span>
                </div>
                <div class="w-8 h-0.5 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-semibold">3</div>
                    <span class="ml-2 text-sm font-medium text-gray-600">Confirmation</span>
                </div>
            </div>
        </div>

        <!-- Booking Form -->
        <form action="{{ route('catering.order.store') }}" method="POST" id="catering-form" class="bg-white rounded-lg shadow-md p-6">
            @csrf

            <!-- Customer Information -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Contact Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="customer_name" class="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                        <input type="text" id="customer_name" name="customer_name" value="{{ old('customer_name') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                        @error('customer_name')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="customer_email" class="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
                        <input type="email" id="customer_email" name="customer_email" value="{{ old('customer_email') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                        @error('customer_email')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="customer_phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number *</label>
                        <input type="tel" id="customer_phone" name="customer_phone" value="{{ old('customer_phone') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                        @error('customer_phone')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="company_name" class="block text-sm font-medium text-gray-700 mb-1">Company Name (Optional)</label>
                        <input type="text" id="company_name" name="company_name" value="{{ old('company_name') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                    </div>
                </div>
            </div>

            <!-- Event Details -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Event Details</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="event_name" class="block text-sm font-medium text-gray-700 mb-1">Event Name</label>
                        <input type="text" id="event_name" name="event_name" value="{{ old('event_name') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                    </div>

                    <div>
                        <label for="catering_event_type_id" class="block text-sm font-medium text-gray-700 mb-1">Event Type *</label>
                        <select id="catering_event_type_id" name="catering_event_type_id" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                            <option value="">Select Event Type</option>
                            @foreach($eventTypes as $eventType)
                            <option value="{{ $eventType->id }}" {{ old('catering_event_type_id') == $eventType->id ? 'selected' : '' }}>
                                {{ $eventType->name }}
                            </option>
                            @endforeach
                        </select>
                        @error('catering_event_type_id')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="event_date" class="block text-sm font-medium text-gray-700 mb-1">Event Date *</label>
                        <input type="date" id="event_date" name="event_date" value="{{ old('event_date') }}" required
                               min="{{ date('Y-m-d', strtotime('+2 days')) }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                        @error('event_date')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="event_start_time" class="block text-sm font-medium text-gray-700 mb-1">Start Time *</label>
                        <input type="time" id="event_start_time" name="event_start_time" value="{{ old('event_start_time') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                        @error('event_start_time')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="event_end_time" class="block text-sm font-medium text-gray-700 mb-1">End Time (Optional)</label>
                        <input type="time" id="event_end_time" name="event_end_time" value="{{ old('event_end_time') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                    </div>

                    <div>
                        <label for="guest_count" class="block text-sm font-medium text-gray-700 mb-1">Number of Guests *</label>
                        <input type="number" id="guest_count" name="guest_count" value="{{ old('guest_count', $guestCount) }}" required
                               min="1" max="1000"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                        @error('guest_count')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="mt-4">
                    <label for="event_description" class="block text-sm font-medium text-gray-700 mb-1">Event Description</label>
                    <textarea id="event_description" name="event_description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">{{ old('event_description') }}</textarea>
                </div>
            </div>

            <!-- Location -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Event Location</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="md:col-span-2">
                        <label for="event_address" class="block text-sm font-medium text-gray-700 mb-1">Address *</label>
                        <input type="text" id="event_address" name="event_address" value="{{ old('event_address') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                        @error('event_address')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="event_city" class="block text-sm font-medium text-gray-700 mb-1">City *</label>
                        <input type="text" id="event_city" name="event_city" value="{{ old('event_city') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                        @error('event_city')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="event_postal_code" class="block text-sm font-medium text-gray-700 mb-1">Postal Code *</label>
                        <input type="text" id="event_postal_code" name="event_postal_code" value="{{ old('event_postal_code') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                        @error('event_postal_code')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="mt-4">
                    <label for="venue_details" class="block text-sm font-medium text-gray-700 mb-1">Venue Details</label>
                    <textarea id="venue_details" name="venue_details" rows="2"
                              placeholder="Indoor/outdoor, kitchen access, parking, etc."
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">{{ old('venue_details') }}</textarea>
                </div>
            </div>

            <!-- Package Selection -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Select Catering Package</h2>
                <div id="packages-container">
                    <p class="text-gray-500">Please select an event type and enter guest count to see available packages.</p>
                </div>
            </div>

            <!-- Additional Services -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Additional Services</h2>
                <div class="space-y-4">
                    <label class="flex items-center">
                        <input type="checkbox" name="needs_setup_service" value="1" {{ old('needs_setup_service') ? 'checked' : '' }}
                               class="rounded border-gray-300 text-orange-600 focus:ring-orange-500">
                        <span class="ml-2 text-sm text-gray-700">Setup Service</span>
                    </label>

                    <label class="flex items-center">
                        <input type="checkbox" name="needs_cleanup_service" value="1" {{ old('needs_cleanup_service') ? 'checked' : '' }}
                               class="rounded border-gray-300 text-orange-600 focus:ring-orange-500">
                        <span class="ml-2 text-sm text-gray-700">Cleanup Service</span>
                    </label>

                    <label class="flex items-center">
                        <input type="checkbox" name="needs_serving_staff" value="1" {{ old('needs_serving_staff') ? 'checked' : '' }}
                               class="rounded border-gray-300 text-orange-600 focus:ring-orange-500" id="needs_serving_staff">
                        <span class="ml-2 text-sm text-gray-700">Professional Serving Staff</span>
                    </label>

                    <div id="staff_count_container" class="ml-6 hidden">
                        <label for="serving_staff_count" class="block text-sm font-medium text-gray-700 mb-1">Number of Staff</label>
                        <input type="number" id="serving_staff_count" name="serving_staff_count" value="{{ old('serving_staff_count', 0) }}"
                               min="0" max="20"
                               class="w-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                    </div>
                </div>
            </div>

            <!-- Payment Method -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Payment Method</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="payment_method" value="credit_card" {{ old('payment_method') == 'credit_card' ? 'checked' : '' }}
                               class="text-orange-600 focus:ring-orange-500">
                        <span class="ml-3">
                            <i class="fas fa-credit-card text-lg mr-2"></i>
                            Credit Card
                        </span>
                    </label>

                    <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="payment_method" value="bank_transfer" {{ old('payment_method') == 'bank_transfer' ? 'checked' : '' }}
                               class="text-orange-600 focus:ring-orange-500">
                        <span class="ml-3">
                            <i class="fas fa-university text-lg mr-2"></i>
                            Bank Transfer
                        </span>
                    </label>
                </div>
                @error('payment_method')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                @enderror
            </div>

            <!-- Pricing Summary -->
            <div id="pricing-summary" class="mb-8 bg-gray-50 rounded-lg p-6 hidden">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Pricing Summary</h2>
                <div id="pricing-details">
                    <!-- Pricing details will be populated by JavaScript -->
                </div>
            </div>

            <!-- Submit Button -->
            <div class="text-center">
                <button type="submit" class="bg-orange-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-700 transition-colors">
                    Submit Catering Request
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    let selectedPackageId = null;

    // Show/hide staff count input
    $('#needs_serving_staff').change(function() {
        if ($(this).is(':checked')) {
            $('#staff_count_container').removeClass('hidden');
        } else {
            $('#staff_count_container').addClass('hidden');
            $('#serving_staff_count').val(0);
        }
        updatePricing();
    });

    // Load packages when event type or guest count changes
    $('#catering_event_type_id, #guest_count').change(function() {
        loadAvailablePackages();
    });

    // Update pricing when form values change
    $('#catering_package_id, #serving_staff_count, input[name="needs_setup_service"], input[name="needs_cleanup_service"]').change(function() {
        updatePricing();
    });

    // Check availability when date/time changes
    $('#event_date, #event_start_time, #catering_event_type_id').change(function() {
        checkAvailability();
    });

    function loadAvailablePackages() {
        const eventTypeId = $('#catering_event_type_id').val();
        const guestCount = $('#guest_count').val();

        if (!eventTypeId || !guestCount) {
            $('#packages-container').html('<p class="text-gray-500">Please select an event type and enter guest count to see available packages.</p>');
            return;
        }

        $('#packages-container').html('<div class="text-center py-4"><div class="spinner mx-auto"></div><p class="mt-2 text-gray-500">Loading packages...</p></div>');

        $.get('{{ route("catering.api.packages") }}', {
            event_type_id: eventTypeId,
            guest_count: guestCount
        })
        .done(function(data) {
            if (data.packages.length === 0) {
                $('#packages-container').html('<p class="text-gray-500">No packages available for the selected criteria.</p>');
                return;
            }

            let packagesHtml = '<div class="grid grid-cols-1 md:grid-cols-2 gap-4">';

            data.packages.forEach(function(package) {
                const isSelected = selectedPackageId == package.id;
                const pricing = package.pricing;

                packagesHtml += `
                    <label class="package-option cursor-pointer">
                        <input type="radio" name="catering_package_id" value="${package.id}"
                               class="hidden package-radio" ${isSelected ? 'checked' : ''}>
                        <div class="border-2 rounded-lg p-4 transition-all ${isSelected ? 'border-orange-500 bg-orange-50' : 'border-gray-300 hover:border-orange-300'}">
                            ${package.is_popular ? '<span class="inline-block bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full mb-2">Popular</span>' : ''}
                            <h3 class="font-semibold text-lg mb-2">${package.name}</h3>
                            <p class="text-gray-600 text-sm mb-3">${package.short_description || ''}</p>

                            ${pricing ? `
                                <div class="mb-3">
                                    <p class="text-lg font-bold text-orange-600">$${pricing.total_amount.toFixed(2)} total</p>
                                    <p class="text-sm text-gray-500">$${pricing.price_per_person.toFixed(2)} per person</p>
                                </div>
                            ` : `
                                <div class="mb-3">
                                    <p class="text-lg font-bold text-orange-600">From $${package.base_price_per_person.toFixed(2)}/person</p>
                                </div>
                            `}

                            ${package.dietary_options && package.dietary_options.length > 0 ? `
                                <div class="mb-3">
                                    <div class="flex flex-wrap gap-1">
                                        ${package.dietary_options.map(option => `<span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">${option}</span>`).join('')}
                                    </div>
                                </div>
                            ` : ''}

                            ${package.service_includes && package.service_includes.length > 0 ? `
                                <div class="text-sm text-gray-600">
                                    <p class="font-medium mb-1">Includes:</p>
                                    <ul class="list-disc list-inside">
                                        ${package.service_includes.slice(0, 3).map(service => `<li>${service}</li>`).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                        </div>
                    </label>
                `;
            });

            packagesHtml += '</div>';
            $('#packages-container').html(packagesHtml);

            // Add event listeners to package options
            $('.package-radio').change(function() {
                selectedPackageId = $(this).val();
                updatePackageSelection();
                updatePricing();
            });
        })
        .fail(function() {
            $('#packages-container').html('<p class="text-red-500">Error loading packages. Please try again.</p>');
        });
    }

    function updatePackageSelection() {
        $('.package-option').each(function() {
            const radio = $(this).find('.package-radio');
            const container = $(this).find('div');

            if (radio.is(':checked')) {
                container.removeClass('border-gray-300').addClass('border-orange-500 bg-orange-50');
            } else {
                container.removeClass('border-orange-500 bg-orange-50').addClass('border-gray-300');
            }
        });
    }

    function updatePricing() {
        const packageId = $('input[name="catering_package_id"]:checked').val();
        const guestCount = $('#guest_count').val();

        if (!packageId || !guestCount) {
            $('#pricing-summary').addClass('hidden');
            return;
        }

        const data = {
            package_id: packageId,
            guest_count: guestCount,
            serving_staff_count: $('#serving_staff_count').val() || 0,
            needs_setup_service: $('input[name="needs_setup_service"]').is(':checked'),
            needs_cleanup_service: $('input[name="needs_cleanup_service"]').is(':checked')
        };

        $.post('{{ route("catering.api.pricing") }}', data)
        .done(function(response) {
            if (response.success) {
                displayPricing(response.pricing);
                $('#pricing-summary').removeClass('hidden');
            }
        })
        .fail(function(xhr) {
            const error = xhr.responseJSON?.error || 'Error calculating pricing';
            showToast(error, 'error');
        });
    }

    function displayPricing(pricing) {
        let pricingHtml = `
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span>Base Amount (${$('#guest_count').val()} guests):</span>
                    <span>$${pricing.base_amount.toFixed(2)}</span>
                </div>
        `;

        if (pricing.setup_fee > 0) {
            pricingHtml += `
                <div class="flex justify-between">
                    <span>Setup Fee:</span>
                    <span>$${pricing.setup_fee.toFixed(2)}</span>
                </div>
            `;
        }

        if (pricing.service_fee > 0) {
            pricingHtml += `
                <div class="flex justify-between">
                    <span>Service Fee:</span>
                    <span>$${pricing.service_fee.toFixed(2)}</span>
                </div>
            `;
        }

        if (pricing.delivery_fee > 0) {
            pricingHtml += `
                <div class="flex justify-between">
                    <span>Delivery Fee:</span>
                    <span>$${pricing.delivery_fee.toFixed(2)}</span>
                </div>
            `;
        }

        if (pricing.staff_fee > 0) {
            pricingHtml += `
                <div class="flex justify-between">
                    <span>Staff Fee:</span>
                    <span>$${pricing.staff_fee.toFixed(2)}</span>
                </div>
            `;
        }

        pricingHtml += `
                <div class="flex justify-between">
                    <span>Tax:</span>
                    <span>$${pricing.tax_amount.toFixed(2)}</span>
                </div>
                <div class="border-t pt-2 flex justify-between font-bold text-lg">
                    <span>Total:</span>
                    <span class="text-orange-600">$${pricing.total_amount.toFixed(2)}</span>
                </div>
            </div>
        `;

        $('#pricing-details').html(pricingHtml);
    }

    function checkAvailability() {
        const eventDate = $('#event_date').val();
        const eventTime = $('#event_start_time').val();
        const eventTypeId = $('#catering_event_type_id').val();

        if (!eventDate || !eventTime || !eventTypeId) {
            return;
        }

        $.post('{{ route("catering.api.availability") }}', {
            event_date: eventDate,
            event_time: eventTime,
            event_type_id: eventTypeId
        })
        .done(function(response) {
            if (!response.available) {
                showToast(response.message, 'error');
            }
        })
        .fail(function() {
            showToast('Error checking availability', 'error');
        });
    }

    // Initialize if values are pre-filled
    if ($('#catering_event_type_id').val() && $('#guest_count').val()) {
        loadAvailablePackages();
    }

    if ($('#needs_serving_staff').is(':checked')) {
        $('#staff_count_container').removeClass('hidden');
    }
});
</script>
@endpush
