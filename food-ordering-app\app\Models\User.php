<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'mobile',
        'password',
        'phone',
        'address',
        'role',
        'is_active',
        'last_login_at',
        'mobile_verified_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'mobile_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
            'last_login_at' => 'datetime',
        ];
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return in_array($this->role, ['admin', 'super_admin']);
    }

    /**
     * Check if user is super admin
     */
    public function isSuperAdmin(): bool
    {
        return $this->role === 'super_admin';
    }

    /**
     * Check if user is customer
     */
    public function isCustomer(): bool
    {
        return $this->role === 'customer';
    }

    /**
     * Check if mobile is verified
     */
    public function isMobileVerified(): bool
    {
        return !is_null($this->mobile_verified_at);
    }

    /**
     * Mark mobile as verified
     */
    public function markMobileAsVerified(): void
    {
        $this->mobile_verified_at = now();
        $this->save();
    }

    /**
     * Format mobile number for display
     */
    public function getFormattedMobileAttribute(): string
    {
        $mobile = $this->mobile;
        if (strlen($mobile) === 10) {
            return '(' . substr($mobile, 0, 3) . ') ' . substr($mobile, 3, 3) . '-' . substr($mobile, 6);
        }
        return $mobile;
    }

    /**
     * Get user's orders
     */
    public function orders()
    {
        return $this->hasMany(\App\Models\Order::class);
    }

    /**
     * Get user's cart items
     */
    public function cartItems()
    {
        return $this->hasMany(\App\Models\CartItem::class);
    }
}
