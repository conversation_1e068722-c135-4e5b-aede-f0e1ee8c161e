<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CateringPackage extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'short_description',
        'image_url',
        'base_price_per_person',
        'min_guests',
        'max_guests',
        'included_items',
        'dietary_options',
        'service_includes',
        'preparation_time_hours',
        'is_popular',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'base_price_per_person' => 'decimal:2',
        'included_items' => 'array',
        'dietary_options' => 'array',
        'service_includes' => 'array',
        'is_popular' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the pricing tiers for this package.
     */
    public function pricingTiers(): HasMany
    {
        return $this->hasMany(CateringPricingTier::class);
    }

    /**
     * Get the catering orders for this package.
     */
    public function cateringOrders(): HasMany
    {
        return $this->hasMany(CateringOrder::class);
    }

    /**
     * Scope to get only active packages.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get popular packages.
     */
    public function scopePopular($query)
    {
        return $query->where('is_popular', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Check if the package is available for the given guest count.
     */
    public function isAvailableForGuests(int $guestCount): bool
    {
        return $guestCount >= $this->min_guests && $guestCount <= $this->max_guests;
    }

    /**
     * Get the pricing for a specific guest count.
     */
    public function getPricingForGuests(int $guestCount): ?CateringPricingTier
    {
        return $this->pricingTiers()
            ->where('min_guests', '<=', $guestCount)
            ->where('max_guests', '>=', $guestCount)
            ->first();
    }

    /**
     * Calculate total price for given guest count.
     */
    public function calculateTotalPrice(int $guestCount, array $options = []): array
    {
        $pricing = $this->getPricingForGuests($guestCount);
        
        if (!$pricing) {
            // Fallback to base price if no tier found
            $pricePerPerson = $this->base_price_per_person;
            $setupFee = 0;
            $serviceFee = 0;
            $deliveryFee = 0;
        } else {
            $pricePerPerson = $pricing->price_per_person;
            $setupFee = $pricing->setup_fee;
            $serviceFee = $pricing->service_fee;
            $deliveryFee = $pricing->delivery_fee;
        }

        $baseAmount = $pricePerPerson * $guestCount;
        
        // Add optional services
        $staffFee = 0;
        if (isset($options['serving_staff_count']) && $options['serving_staff_count'] > 0) {
            $staffFee = $options['serving_staff_count'] * 150; // $150 per staff member
        }

        $subtotal = $baseAmount + $setupFee + $serviceFee + $deliveryFee + $staffFee;
        $taxRate = 0.08; // 8% tax
        $taxAmount = $subtotal * $taxRate;
        $total = $subtotal + $taxAmount;

        return [
            'base_amount' => $baseAmount,
            'setup_fee' => $setupFee,
            'service_fee' => $serviceFee,
            'delivery_fee' => $deliveryFee,
            'staff_fee' => $staffFee,
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'total_amount' => $total,
            'price_per_person' => $pricePerPerson,
        ];
    }

    /**
     * Get the minimum preparation time.
     */
    public function getMinimumPreparationTime(): \Carbon\Carbon
    {
        return now()->addHours($this->preparation_time_hours);
    }
}
