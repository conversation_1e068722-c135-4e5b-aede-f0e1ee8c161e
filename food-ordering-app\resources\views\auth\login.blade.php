@extends('layouts.app')

@section('title', 'Login - Food Ordering App')

@push('styles')
<style>
    .auth-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: calc(100vh - 140px); /* Account for navbar and footer */
        display: flex;
        align-items: center;
        padding: 2rem 0;
    }

    .login-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: none;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border-radius: 1rem;
    }

    /* Mobile-first responsive styles */
    @media (max-width: 576px) {
        .login-card .card-body {
            padding: 1.5rem !important;
        }

        .login-card h2 {
            font-size: 1.5rem;
        }

        .auth-container {
            min-height: calc(100vh - 120px);
            padding: 1rem 0;
        }
    }

    @media (min-width: 577px) and (max-width: 768px) {
        .login-card .card-body {
            padding: 2.5rem !important;
        }
    }

    /* Touch-friendly form elements */
    @media (max-width: 768px) {
        .form-control, .btn {
            min-height: 48px;
        }

        .input-group-text {
            min-width: 48px;
            justify-content: center;
        }
    }
</style>
@endpush

@section('content')
<div class="auth-container">
    <div class="container-mobile">
        <div class="flex justify-center">
            <div class="w-full max-w-md">
                <div class="card login-card">
                    <div class="card-body p-3 p-sm-4 p-md-5">
                        <div class="text-center mb-4">
                            <h2 class="fw-bold text-primary">Welcome Back</h2>
                            <p class="text-muted">Sign in with your mobile number or email</p>
                        </div>

                        @if($errors->any())
                            <div class="alert alert-danger">
                                @foreach($errors->all() as $error)
                                    <div>{{ $error }}</div>
                                @endforeach
                            </div>
                        @endif

                        @if(session('error'))
                            <div class="alert alert-danger">
                                {{ session('error') }}
                            </div>
                        @endif

                        <form method="POST" action="{{ route('login') }}">
                            @csrf

                            <div class="mb-3">
                                <label for="login" class="form-label">Mobile Number or Email</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-person-circle"></i>
                                    </span>
                                    <input type="text" class="form-control @error('login') is-invalid @enderror"
                                           id="login" name="login" value="{{ old('login') }}" required autofocus
                                           placeholder="Enter mobile number or email">
                                </div>
                                @error('login')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">Use your 10-digit mobile number or email address</small>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock"></i>
                                    </span>
                                    <input type="password" class="form-control @error('password') is-invalid @enderror"
                                           id="password" name="password" required>
                                </div>
                                @error('password')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    Remember me
                                </label>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>
                                    Sign In
                                </button>
                            </div>
                        </form>

                        <hr class="my-4">

                        <div class="text-center">
                            <p class="mb-3">Don't have an account?</p>
                            <a href="{{ route('register') }}" class="btn btn-outline-success">
                                <i class="bi bi-person-plus me-1"></i>
                                Create Account
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
