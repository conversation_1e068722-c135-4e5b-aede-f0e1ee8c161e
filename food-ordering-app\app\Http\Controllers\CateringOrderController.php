<?php

namespace App\Http\Controllers;

use App\Models\CateringOrder;
use App\Models\CateringEventType;
use App\Models\CateringPackage;
use App\Models\CateringOrderItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class CateringOrderController extends Controller
{
    /**
     * Store a new catering order.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            // Customer Information
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email|max:255',
            'customer_phone' => 'required|string|max:20',
            'company_name' => 'nullable|string|max:255',
            
            // Event Details
            'event_name' => 'nullable|string|max:255',
            'event_date' => 'required|date|after:today',
            'event_start_time' => 'required|date_format:H:i',
            'event_end_time' => 'nullable|date_format:H:i|after:event_start_time',
            'guest_count' => 'required|integer|min:1|max:1000',
            'event_description' => 'nullable|string|max:1000',
            
            // Location
            'event_address' => 'required|string|max:500',
            'event_city' => 'required|string|max:100',
            'event_state' => 'nullable|string|max:50',
            'event_postal_code' => 'required|string|max:20',
            'venue_details' => 'nullable|string|max:1000',
            
            // Catering Details
            'catering_event_type_id' => 'required|exists:catering_event_types,id',
            'catering_package_id' => 'required|exists:catering_packages,id',
            'dietary_requirements' => 'nullable|array',
            'special_requests' => 'nullable|array',
            'needs_setup_service' => 'boolean',
            'needs_cleanup_service' => 'boolean',
            'needs_serving_staff' => 'boolean',
            'serving_staff_count' => 'nullable|integer|min:0|max:20',
            
            // Payment
            'payment_method' => 'required|in:credit_card,bank_transfer,cash,check',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            // Validate event type and package
            $eventType = CateringEventType::findOrFail($request->catering_event_type_id);
            $package = CateringPackage::findOrFail($request->catering_package_id);

            // Check if package is available for guest count
            if (!$package->isAvailableForGuests($request->guest_count)) {
                return back()->withErrors([
                    'guest_count' => "This package is only available for {$package->min_guests}-{$package->max_guests} guests."
                ])->withInput();
            }

            // Check availability
            $eventDateTime = \Carbon\Carbon::createFromFormat(
                'Y-m-d H:i', 
                $request->event_date . ' ' . $request->event_start_time
            );

            if (!$eventType->isDateTimeAvailable($eventDateTime)) {
                return back()->withErrors([
                    'event_date' => 'The selected date and time is not available for this event type.'
                ])->withInput();
            }

            // Calculate pricing
            $options = [
                'serving_staff_count' => $request->get('serving_staff_count', 0),
                'needs_setup_service' => $request->boolean('needs_setup_service'),
                'needs_cleanup_service' => $request->boolean('needs_cleanup_service'),
            ];

            $pricing = $package->calculateTotalPrice($request->guest_count, $options);

            // Create the catering order
            $cateringOrder = CateringOrder::create([
                'order_number' => CateringOrder::generateOrderNumber(),
                'user_id' => auth()->id(),
                'catering_event_type_id' => $request->catering_event_type_id,
                'catering_package_id' => $request->catering_package_id,
                'customer_name' => $request->customer_name,
                'customer_email' => $request->customer_email,
                'customer_phone' => $request->customer_phone,
                'company_name' => $request->company_name,
                'event_name' => $request->event_name,
                'event_date' => $request->event_date,
                'event_start_time' => $request->event_start_time,
                'event_end_time' => $request->event_end_time,
                'guest_count' => $request->guest_count,
                'event_description' => $request->event_description,
                'event_address' => $request->event_address,
                'event_city' => $request->event_city,
                'event_state' => $request->event_state,
                'event_postal_code' => $request->event_postal_code,
                'venue_details' => $request->venue_details,
                'dietary_requirements' => $request->dietary_requirements,
                'special_requests' => $request->special_requests,
                'needs_setup_service' => $request->boolean('needs_setup_service'),
                'needs_cleanup_service' => $request->boolean('needs_cleanup_service'),
                'needs_serving_staff' => $request->boolean('needs_serving_staff'),
                'serving_staff_count' => $request->get('serving_staff_count', 0),
                'base_amount' => $pricing['base_amount'],
                'setup_fee' => $pricing['setup_fee'],
                'service_fee' => $pricing['service_fee'],
                'delivery_fee' => $pricing['delivery_fee'],
                'staff_fee' => $pricing['staff_fee'],
                'tax_amount' => $pricing['tax_amount'],
                'total_amount' => $pricing['total_amount'],
                'payment_method' => $request->payment_method,
                'status' => 'pending',
                'payment_status' => 'pending',
            ]);

            // Create order items based on package contents
            if ($package->included_items) {
                foreach ($package->included_items as $item) {
                    CateringOrderItem::create([
                        'catering_order_id' => $cateringOrder->id,
                        'item_type' => 'package_item',
                        'item_name' => $item['name'] ?? 'Package Item',
                        'item_description' => $item['description'] ?? '',
                        'quantity' => $item['quantity'] ?? 1,
                        'unit_price' => 0, // Included in package price
                        'total_price' => 0,
                    ]);
                }
            }

            DB::commit();

            return redirect()->route('catering.confirmation', $cateringOrder->order_number)
                ->with('success', 'Your catering order has been submitted successfully!');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return back()->withErrors([
                'general' => 'An error occurred while processing your order. Please try again.'
            ])->withInput();
        }
    }

    /**
     * Show order confirmation page.
     */
    public function confirmation(CateringOrder $cateringOrder)
    {
        $cateringOrder->load(['eventType', 'package', 'orderItems']);
        
        return view('catering.confirmation', compact('cateringOrder'));
    }

    /**
     * Show order tracking page.
     */
    public function track(CateringOrder $cateringOrder)
    {
        $cateringOrder->load(['eventType', 'package', 'orderItems']);
        
        return view('catering.track', compact('cateringOrder'));
    }

    /**
     * Display a listing of catering orders (admin).
     */
    public function index(Request $request)
    {
        $query = CateringOrder::with(['eventType', 'package'])
            ->orderBy('event_date', 'desc')
            ->orderBy('created_at', 'desc');

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('event_date', '>=', $request->date_from);
        }
        
        if ($request->filled('date_to')) {
            $query->where('event_date', '<=', $request->date_to);
        }

        $orders = $query->paginate(20);

        return view('catering.admin.index', compact('orders'));
    }

    /**
     * Show the specified catering order (admin).
     */
    public function show(CateringOrder $cateringOrder)
    {
        $cateringOrder->load(['eventType', 'package', 'orderItems', 'user']);
        
        return view('catering.admin.show', compact('cateringOrder'));
    }

    /**
     * Update the order status.
     */
    public function updateStatus(Request $request, CateringOrder $cateringOrder)
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,in_preparation,ready,in_transit,delivered,completed,cancelled',
            'notes' => 'nullable|string|max:1000',
        ]);

        $cateringOrder->updateStatus($request->status, $request->notes);

        return back()->with('success', 'Order status updated successfully.');
    }
}
