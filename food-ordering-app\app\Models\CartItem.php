<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CartItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'session_id',
        'user_id',
        'food_item_id',
        'package_id',
        'quantity',
        'unit_weight',
        'unit_price',
        'total_price',
        'order_type',
        'customizations',
        'special_instructions',
    ];

    protected $casts = [
        'customizations' => 'array',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'unit_weight' => 'decimal:3',
    ];

    /**
     * Get the user that owns the cart item.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the food item for this cart item.
     */
    public function foodItem(): BelongsTo
    {
        return $this->belongsTo(FoodItem::class);
    }

    /**
     * Get the package for this cart item.
     */
    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class);
    }

    /**
     * Get the item (either food item or package).
     */
    public function getItemAttribute()
    {
        return $this->food_item_id ? $this->foodItem : $this->package;
    }

    /**
     * Get the item name.
     */
    public function getItemNameAttribute(): string
    {
        return $this->item ? $this->item->name : '';
    }

    /**
     * Get the item image.
     */
    public function getItemImageAttribute(): ?string
    {
        return $this->item ? $this->item->image : null;
    }

    /**
     * Scope to get cart items for a specific session.
     */
    public function scopeForSession($query, $sessionId)
    {
        return $query->where('session_id', $sessionId);
    }

    /**
     * Scope to get cart items for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Calculate and update the total price.
     */
    public function calculateTotalPrice(): void
    {
        if ($this->order_type === 'bulk' && $this->unit_weight) {
            $this->total_price = $this->unit_price * $this->unit_weight;
        } else {
            $this->total_price = $this->unit_price * $this->quantity;
        }
    }

    /**
     * Update the cart item with new quantity or weight.
     */
    public function updateQuantity(int $quantity, ?float $weight = null): void
    {
        $this->quantity = $quantity;
        
        if ($weight !== null) {
            $this->unit_weight = $weight;
        }
        
        $this->calculateTotalPrice();
        $this->save();
    }
}
