<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CateringOrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'catering_order_id',
        'item_type',
        'item_id',
        'item_name',
        'item_description',
        'quantity',
        'unit_price',
        'total_price',
        'customizations',
        'special_instructions',
    ];

    protected $casts = [
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'customizations' => 'array',
    ];

    /**
     * Get the catering order that owns this item.
     */
    public function cateringOrder(): BelongsTo
    {
        return $this->belongsTo(CateringOrder::class);
    }

    /**
     * Get the related item (polymorphic relationship).
     */
    public function item()
    {
        switch ($this->item_type) {
            case 'food_item':
                return $this->belongsTo(FoodItem::class, 'item_id');
            case 'package_item':
                return $this->belongsTo(Package::class, 'item_id');
            default:
                return null;
        }
    }

    /**
     * Calculate the total price based on quantity and unit price.
     */
    public function calculateTotalPrice(): void
    {
        $this->total_price = $this->quantity * $this->unit_price;
    }

    /**
     * Get formatted customizations as a string.
     */
    public function getCustomizationsStringAttribute(): string
    {
        if (!$this->customizations) {
            return '';
        }

        return collect($this->customizations)
            ->map(function ($value, $key) {
                return ucfirst($key) . ': ' . $value;
            })
            ->implode(', ');
    }
}
