<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('catering_orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number')->unique();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->unsignedBigInteger('catering_event_type_id');
            $table->unsignedBigInteger('catering_package_id');

            // Customer Information
            $table->string('customer_name');
            $table->string('customer_email');
            $table->string('customer_phone');
            $table->string('company_name')->nullable();

            // Event Details
            $table->string('event_name')->nullable();
            $table->date('event_date');
            $table->time('event_start_time');
            $table->time('event_end_time')->nullable();
            $table->integer('guest_count');
            $table->text('event_description')->nullable();

            // Location
            $table->text('event_address');
            $table->string('event_city');
            $table->string('event_state')->nullable();
            $table->string('event_postal_code');
            $table->text('venue_details')->nullable(); // indoor/outdoor, kitchen access, etc.

            // Catering Specifics
            $table->json('dietary_requirements')->nullable(); // allergies, restrictions
            $table->json('special_requests')->nullable();
            $table->boolean('needs_setup_service')->default(false);
            $table->boolean('needs_cleanup_service')->default(false);
            $table->boolean('needs_serving_staff')->default(false);
            $table->integer('serving_staff_count')->default(0);

            // Pricing
            $table->decimal('base_amount', 10, 2);
            $table->decimal('setup_fee', 8, 2)->default(0);
            $table->decimal('service_fee', 8, 2)->default(0);
            $table->decimal('delivery_fee', 8, 2)->default(0);
            $table->decimal('staff_fee', 8, 2)->default(0);
            $table->decimal('tax_amount', 8, 2)->default(0);
            $table->decimal('discount_amount', 8, 2)->default(0);
            $table->decimal('total_amount', 10, 2);

            // Order Management
            $table->enum('status', [
                'pending', 'confirmed', 'in_preparation', 'ready',
                'in_transit', 'delivered', 'completed', 'cancelled'
            ])->default('pending');
            $table->enum('payment_status', [
                'pending', 'partial', 'paid', 'refunded'
            ])->default('pending');
            $table->string('payment_method')->nullable();

            // Timestamps
            $table->timestamp('confirmed_at')->nullable();
            $table->timestamp('preparation_started_at')->nullable();
            $table->timestamp('ready_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('cancelled_at')->nullable();

            $table->text('notes')->nullable();
            $table->text('cancellation_reason')->nullable();
            $table->json('status_history')->nullable();

            $table->timestamps();

            $table->index(['event_date', 'status'], 'catering_orders_date_status_idx');
            $table->index(['customer_email', 'event_date'], 'catering_orders_email_date_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('catering_orders');
    }
};
