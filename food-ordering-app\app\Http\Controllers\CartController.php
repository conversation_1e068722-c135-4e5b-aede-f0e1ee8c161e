<?php

namespace App\Http\Controllers;

use App\Models\CartItem;
use App\Models\FoodItem;
use App\Models\Package;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class CartController extends Controller
{
    /**
     * Display the shopping cart.
     */
    public function index()
    {
        $cartItems = $this->getCartItems();
        $cartTotal = $cartItems->sum('total_price');
        $cartCount = $cartItems->sum('quantity');

        return view('cart.index', compact('cartItems', 'cartTotal', 'cartCount'));
    }

    /**
     * Add item to cart.
     */
    public function addItem(Request $request)
    {
        $request->validate([
            'item_type' => 'required|in:food_item,package',
            'item_id' => 'required|integer',
            'quantity' => 'required|integer|min:1',
            'order_type' => 'required|in:unit,bulk',
            'unit_weight' => 'nullable|numeric|min:0.1',
            'special_instructions' => 'nullable|string|max:500',
        ]);

        $itemType = $request->item_type;
        $itemId = $request->item_id;
        $quantity = $request->quantity;
        $orderType = $request->order_type;
        $unitWeight = $request->unit_weight;

        // Get the item
        if ($itemType === 'food_item') {
            $item = FoodItem::findOrFail($itemId);
            $foodItemId = $itemId;
            $packageId = null;
        } else {
            $item = Package::findOrFail($itemId);
            $foodItemId = null;
            $packageId = $itemId;
        }

        // Validate bulk order
        if ($orderType === 'bulk') {
            if ($itemType === 'food_item' && !$item->allow_bulk_order) {
                return response()->json(['error' => 'This item is not available for bulk ordering.'], 400);
            }
            if (!$unitWeight) {
                return response()->json(['error' => 'Weight is required for bulk orders.'], 400);
            }
        }

        // Calculate price
        if ($itemType === 'food_item') {
            $unitPrice = $item->getEffectivePrice($orderType, $unitWeight);
        } else {
            $unitPrice = $item->price;
        }

        $totalPrice = $orderType === 'bulk' ? $unitPrice : ($unitPrice * $quantity);

        // Check if item already exists in cart
        $existingCartItem = CartItem::where('session_id', Session::getId())
            ->where('food_item_id', $foodItemId)
            ->where('package_id', $packageId)
            ->where('order_type', $orderType)
            ->first();

        if ($existingCartItem) {
            // Update existing item
            if ($orderType === 'bulk') {
                $existingCartItem->unit_weight += $unitWeight;
                $existingCartItem->calculateTotalPrice();
            } else {
                $existingCartItem->quantity += $quantity;
                $existingCartItem->calculateTotalPrice();
            }
            $existingCartItem->save();
        } else {
            // Create new cart item
            CartItem::create([
                'session_id' => Session::getId(),
                'user_id' => auth()->id(),
                'food_item_id' => $foodItemId,
                'package_id' => $packageId,
                'quantity' => $quantity,
                'unit_weight' => $unitWeight,
                'unit_price' => $unitPrice,
                'total_price' => $totalPrice,
                'order_type' => $orderType,
                'special_instructions' => $request->special_instructions,
            ]);
        }

        // Return updated cart info
        $cartItems = $this->getCartItems();
        $cartTotal = $cartItems->sum('total_price');
        $cartCount = $cartItems->sum('quantity');

        return response()->json([
            'success' => true,
            'message' => 'Item added to cart successfully!',
            'cart_count' => $cartCount,
            'cart_total' => number_format($cartTotal, 2),
        ]);
    }

    /**
     * Update cart item quantity.
     */
    public function updateItem(Request $request, CartItem $cartItem)
    {
        $request->validate([
            'quantity' => 'required|integer|min:1',
            'unit_weight' => 'nullable|numeric|min:0.1',
        ]);

        // Verify cart item belongs to current session
        if ($cartItem->session_id !== Session::getId()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $cartItem->updateQuantity($request->quantity, $request->unit_weight);

        $cartItems = $this->getCartItems();
        $cartTotal = $cartItems->sum('total_price');
        $cartCount = $cartItems->sum('quantity');

        return response()->json([
            'success' => true,
            'message' => 'Cart updated successfully!',
            'cart_count' => $cartCount,
            'cart_total' => number_format($cartTotal, 2),
            'item_total' => number_format($cartItem->total_price, 2),
        ]);
    }

    /**
     * Remove item from cart.
     */
    public function removeItem(CartItem $cartItem)
    {
        // Verify cart item belongs to current session
        if ($cartItem->session_id !== Session::getId()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $cartItem->delete();

        $cartItems = $this->getCartItems();
        $cartTotal = $cartItems->sum('total_price');
        $cartCount = $cartItems->sum('quantity');

        return response()->json([
            'success' => true,
            'message' => 'Item removed from cart!',
            'cart_count' => $cartCount,
            'cart_total' => number_format($cartTotal, 2),
        ]);
    }

    /**
     * Clear the entire cart.
     */
    public function clearCart()
    {
        CartItem::where('session_id', Session::getId())->delete();

        return response()->json([
            'success' => true,
            'message' => 'Cart cleared successfully!',
            'cart_count' => 0,
            'cart_total' => '0.00',
        ]);
    }

    /**
     * Get cart items count (AJAX).
     */
    public function getCartCount()
    {
        $cartItems = $this->getCartItems();
        $cartCount = $cartItems->sum('quantity');
        $cartTotal = $cartItems->sum('total_price');

        return response()->json([
            'cart_count' => $cartCount,
            'cart_total' => number_format($cartTotal, 2),
        ]);
    }

    /**
     * Get cart items for current session.
     */
    private function getCartItems()
    {
        return CartItem::where('session_id', Session::getId())
            ->with(['foodItem.category', 'foodItem.cuisine', 'package.category', 'package.cuisine'])
            ->orderBy('created_at', 'desc')
            ->get();
    }
}
