# Advanced Admin Panel Setup Guide

## Overview
This advanced admin panel provides comprehensive management capabilities for your food ordering application, including user management, food item management, order tracking, analytics, and more.

## Features Implemented

### 🔐 Authentication & Authorization
- Role-based access control (Customer, Admin, Super Admin)
- Secure admin middleware
- Login/logout functionality
- Session management

### 📊 Dashboard
- Key metrics and statistics
- Monthly revenue charts
- Order status distribution
- Recent orders and top-selling items
- Real-time data visualization

### 👥 User Management
- Complete CRUD operations for users
- Role assignment and management
- User status control (active/inactive)
- Search and filter functionality
- Bulk operations

### 🍕 Food Item Management
- Full food item CRUD operations
- Image upload and management
- Category and cuisine assignment
- Availability control
- Bulk actions (activate, deactivate, feature, delete)
- Advanced filtering and search

### 📋 Order Management
- Comprehensive order listing and filtering
- Order status tracking and updates
- Payment status management
- Order details view
- Invoice generation
- Export functionality
- Real-time status updates

### 📈 Analytics & Reports
- Revenue tracking
- Order statistics
- Performance metrics
- Export capabilities

## Setup Instructions

### 1. Database Migration
Run the migrations to update the users table with admin fields:

```bash
php artisan migrate
```

### 2. Seed Admin Users
Run the seeder to create admin users:

```bash
php artisan db:seed --class=AdminSeeder
```

### 3. Storage Link (for image uploads)
Create a symbolic link for public storage:

```bash
php artisan storage:link
```

### 4. File Permissions
Ensure proper permissions for storage directories:

```bash
chmod -R 775 storage/
chmod -R 775 bootstrap/cache/
```

## Admin Credentials

After running the seeder, you can login with these credentials:

### Super Admin
- **Email:** <EMAIL>
- **Password:** password123
- **Role:** Super Admin (full access)

### Restaurant Manager
- **Email:** <EMAIL>
- **Password:** password123
- **Role:** Admin (limited access)

### Test Customers
- **Email:** <EMAIL>
- **Password:** password123
- **Role:** Customer

- **Email:** <EMAIL>
- **Password:** password123
- **Role:** Customer

## Accessing the Admin Panel

1. Navigate to `/admin/login` in your browser (dedicated admin login)
2. Use the admin credentials above
3. You'll be automatically redirected to the admin dashboard
4. The admin panel is accessible at `/admin`

**Note:** Admin login is completely separate from customer login:
- **Customer Login:** `/login` (for regular users)
- **Admin Login:** `/admin/login` (for administrators only)

## Admin Panel Structure

### Routes
- `/admin` - Dashboard
- `/admin/users` - User Management
- `/admin/food-items` - Food Item Management
- `/admin/orders` - Order Management

### Key Features

#### Dashboard (`/admin`)
- Overview statistics
- Revenue charts
- Recent activity
- Quick actions

#### User Management (`/admin/users`)
- List all users with pagination
- Search by name, email, phone
- Filter by role and status
- Create, edit, delete users
- Toggle user status
- Role management

#### Food Item Management (`/admin/food-items`)
- Complete food item CRUD
- Image upload and management
- Category and cuisine filtering
- Availability control
- Bulk operations
- Advanced search

#### Order Management (`/admin/orders`)
- Order listing with filters
- Status tracking and updates
- Payment status management
- Order details view
- Invoice generation
- Export to CSV

## Security Features

### Role-Based Access Control
- **Super Admin:** Full access to all features
- **Admin:** Limited access (cannot delete super admins)
- **Customer:** No admin access

### Middleware Protection
- All admin routes protected by authentication
- Role verification on every request
- Session security

### Data Validation
- Comprehensive form validation
- CSRF protection
- Input sanitization

## Customization

### Adding New Admin Features
1. Create controller in `app/Http/Controllers/Admin/`
2. Add routes in `routes/web.php` under admin group
3. Create views in `resources/views/admin/`
4. Update navigation in `resources/views/admin/layouts/app.blade.php`

### Styling
- Bootstrap 5 for responsive design
- Custom CSS in admin layout
- Chart.js for data visualization
- Bootstrap Icons for UI elements

## File Structure

```
app/
├── Http/
│   ├── Controllers/
│   │   ├── Admin/
│   │   │   ├── AdminController.php
│   │   │   ├── UserController.php
│   │   │   ├── FoodItemController.php
│   │   │   └── OrderController.php
│   │   └── Auth/
│   │       └── LoginController.php
│   └── Middleware/
│       └── AdminMiddleware.php
├── Models/
│   └── User.php (updated with roles)
└── ...

resources/
└── views/
    ├── admin/
    │   ├── layouts/
    │   │   └── app.blade.php
    │   ├── dashboard.blade.php
    │   ├── users/
    │   ├── food-items/
    │   └── orders/
    └── auth/
        └── login.blade.php

database/
├── migrations/
│   └── 0001_01_01_000000_create_users_table.php (updated)
└── seeders/
    └── AdminSeeder.php
```

## Troubleshooting

### Common Issues

1. **403 Access Denied**
   - Ensure user has admin role
   - Check middleware is properly registered

2. **Images not displaying**
   - Run `php artisan storage:link`
   - Check file permissions

3. **Charts not loading**
   - Ensure Chart.js is loaded
   - Check browser console for errors

4. **Login redirects to home**
   - Verify admin middleware
   - Check user role in database

### Support
For issues or questions, check the Laravel documentation or create an issue in the project repository.

## Next Steps

Consider implementing these additional features:
- Category and Cuisine management
- Package management
- Catering order management
- Advanced reporting
- Email notifications
- Activity logging
- API endpoints
- Mobile app support
