<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add foreign key constraints for catering_pricing_tiers
        Schema::table('catering_pricing_tiers', function (Blueprint $table) {
            $table->foreign('catering_package_id')->references('id')->on('catering_packages')->onDelete('cascade');
        });

        // Add foreign key constraints for catering_orders
        Schema::table('catering_orders', function (Blueprint $table) {
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('catering_event_type_id')->references('id')->on('catering_event_types')->onDelete('restrict');
            $table->foreign('catering_package_id')->references('id')->on('catering_packages')->onDelete('restrict');
        });

        // Add foreign key constraints for catering_order_items
        Schema::table('catering_order_items', function (Blueprint $table) {
            $table->foreign('catering_order_id')->references('id')->on('catering_orders')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop foreign key constraints for catering_order_items
        Schema::table('catering_order_items', function (Blueprint $table) {
            $table->dropForeign(['catering_order_id']);
        });

        // Drop foreign key constraints for catering_orders
        Schema::table('catering_orders', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
            $table->dropForeign(['catering_event_type_id']);
            $table->dropForeign(['catering_package_id']);
        });

        // Drop foreign key constraints for catering_pricing_tiers
        Schema::table('catering_pricing_tiers', function (Blueprint $table) {
            $table->dropForeign(['catering_package_id']);
        });
    }
};
