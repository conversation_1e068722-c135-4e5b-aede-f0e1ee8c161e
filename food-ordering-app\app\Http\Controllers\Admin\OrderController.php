<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\User;
use Illuminate\Http\Request;
use Carbon\Carbon;

class OrderController extends Controller
{
    /**
     * Display a listing of orders.
     */
    public function index(Request $request)
    {
        $query = Order::with(['user', 'orderItems.foodItem']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%")
                  ->orWhere('customer_email', 'like', "%{$search}%")
                  ->orWhere('customer_phone', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by payment status
        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        // Filter by order type
        if ($request->filled('order_type')) {
            $query->where('order_type', $request->order_type);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $orders = $query->latest()->paginate(15);

        // Get status counts for filter badges
        $statusCounts = Order::selectRaw('status, count(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status');

        return view('admin.orders.index', compact('orders', 'statusCounts'));
    }

    /**
     * Display the specified order.
     */
    public function show(Order $order)
    {
        $order->load(['user', 'orderItems.foodItem']);
        return view('admin.orders.show', compact('order'));
    }

    /**
     * Update order status.
     */
    public function updateStatus(Request $request, Order $order)
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,preparing,ready,out_for_delivery,delivered,cancelled',
            'notes' => 'nullable|string|max:500',
        ]);

        $oldStatus = $order->status;
        $newStatus = $request->status;

        // Update status and timestamp fields
        $updateData = ['status' => $newStatus];

        if ($request->filled('notes')) {
            $updateData['notes'] = $request->notes;
        }

        // Set appropriate timestamp based on status
        switch ($newStatus) {
            case 'confirmed':
                $updateData['confirmed_at'] = now();
                break;
            case 'preparing':
                $updateData['confirmed_at'] = $updateData['confirmed_at'] ?? now();
                break;
            case 'ready':
            case 'out_for_delivery':
                $updateData['prepared_at'] = now();
                break;
            case 'delivered':
                $updateData['delivered_at'] = now();
                break;
            case 'cancelled':
                $updateData['cancelled_at'] = now();
                if ($request->filled('cancellation_reason')) {
                    $updateData['cancellation_reason'] = $request->cancellation_reason;
                }
                break;
        }

        // Update status history
        $statusHistory = $order->status_history ? json_decode($order->status_history, true) : [];
        $statusHistory[] = [
            'status' => $newStatus,
            'changed_at' => now()->toISOString(),
            'changed_by' => auth()->user()->name,
            'notes' => $request->notes,
        ];
        $updateData['status_history'] = json_encode($statusHistory);

        $order->update($updateData);

        return redirect()->back()
            ->with('success', "Order status updated from {$oldStatus} to {$newStatus}.");
    }

    /**
     * Update payment status.
     */
    public function updatePaymentStatus(Request $request, Order $order)
    {
        $request->validate([
            'payment_status' => 'required|in:pending,paid,failed,refunded',
        ]);

        $order->update(['payment_status' => $request->payment_status]);

        return redirect()->back()
            ->with('success', 'Payment status updated successfully.');
    }

    /**
     * Print order invoice.
     */
    public function printInvoice(Order $order)
    {
        $order->load(['user', 'orderItems.foodItem']);
        return view('admin.orders.invoice', compact('order'));
    }

    /**
     * Export orders to CSV.
     */
    public function export(Request $request)
    {
        $query = Order::with(['user', 'orderItems.foodItem']);

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $orders = $query->get();

        $filename = 'orders_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($orders) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Order Number',
                'Customer Name',
                'Customer Email',
                'Customer Phone',
                'Order Type',
                'Status',
                'Payment Status',
                'Subtotal',
                'Tax Amount',
                'Delivery Fee',
                'Total Amount',
                'Order Date',
                'Delivery Address',
            ]);

            // CSV data
            foreach ($orders as $order) {
                fputcsv($file, [
                    $order->order_number,
                    $order->customer_name,
                    $order->customer_email,
                    $order->customer_phone,
                    $order->order_type,
                    $order->status,
                    $order->payment_status,
                    $order->subtotal,
                    $order->tax_amount,
                    $order->delivery_fee,
                    $order->total_amount,
                    $order->created_at->format('Y-m-d H:i:s'),
                    $order->delivery_address,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get order statistics.
     */
    public function statistics()
    {
        $today = Carbon::today();
        $thisWeek = Carbon::now()->startOfWeek();
        $thisMonth = Carbon::now()->startOfMonth();

        $stats = [
            'today' => [
                'orders' => Order::whereDate('created_at', $today)->count(),
                'revenue' => Order::whereDate('created_at', $today)->where('payment_status', 'paid')->sum('total_amount'),
            ],
            'this_week' => [
                'orders' => Order::where('created_at', '>=', $thisWeek)->count(),
                'revenue' => Order::where('created_at', '>=', $thisWeek)->where('payment_status', 'paid')->sum('total_amount'),
            ],
            'this_month' => [
                'orders' => Order::where('created_at', '>=', $thisMonth)->count(),
                'revenue' => Order::where('created_at', '>=', $thisMonth)->where('payment_status', 'paid')->sum('total_amount'),
            ],
            'all_time' => [
                'orders' => Order::count(),
                'revenue' => Order::where('payment_status', 'paid')->sum('total_amount'),
            ],
        ];

        return view('admin.orders.statistics', compact('stats'));
    }
}
