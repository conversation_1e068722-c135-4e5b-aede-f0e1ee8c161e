<?php

namespace Database\Seeders;

use App\Models\Cuisine;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class CuisineSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $cuisines = [
            [
                'name' => 'Indian',
                'slug' => 'indian',
                'description' => 'Authentic Indian cuisine with rich spices and flavors',
                'sort_order' => 1,
            ],
            [
                'name' => 'Chinese',
                'slug' => 'chinese',
                'description' => 'Traditional Chinese dishes with fresh ingredients',
                'sort_order' => 2,
            ],
            [
                'name' => 'Italian',
                'slug' => 'italian',
                'description' => 'Classic Italian pasta, pizza, and more',
                'sort_order' => 3,
            ],
            [
                'name' => 'Mexican',
                'slug' => 'mexican',
                'description' => 'Spicy and flavorful Mexican cuisine',
                'sort_order' => 4,
            ],
            [
                'name' => 'American',
                'slug' => 'american',
                'description' => 'Classic American comfort food',
                'sort_order' => 5,
            ],
            [
                'name' => 'Thai',
                'slug' => 'thai',
                'description' => 'Authentic Thai dishes with perfect balance of flavors',
                'sort_order' => 6,
            ],
            [
                'name' => 'Mediterranean',
                'slug' => 'mediterranean',
                'description' => 'Fresh and healthy Mediterranean cuisine',
                'sort_order' => 7,
            ],
            [
                'name' => 'Japanese',
                'slug' => 'japanese',
                'description' => 'Traditional Japanese sushi and dishes',
                'sort_order' => 8,
            ],
        ];

        foreach ($cuisines as $cuisine) {
            Cuisine::create($cuisine);
        }
    }
}
