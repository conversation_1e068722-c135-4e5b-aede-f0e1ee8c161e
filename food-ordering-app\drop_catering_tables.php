<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';

use Illuminate\Support\Facades\DB;

try {
    // Drop catering tables in reverse order (to handle foreign key constraints)
    $tables = [
        'catering_order_items',
        'catering_orders', 
        'catering_pricing_tiers',
        'catering_packages',
        'catering_event_types'
    ];
    
    foreach ($tables as $table) {
        DB::statement("DROP TABLE IF EXISTS `{$table}`");
        echo "Dropped table: {$table}\n";
    }
    
    echo "All catering tables dropped successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
