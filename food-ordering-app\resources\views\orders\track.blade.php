@extends('layouts.app')

@section('title', 'Track Order - Food Ordering App')

@section('content')
<div class="container-mobile py-6">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h1 class="text-2xl font-bold text-gray-800">Track Your Order</h1>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Order Number</p>
                    <p class="text-lg font-semibold text-orange-600">{{ $order->order_number }}</p>
                </div>
            </div>

            <div class="grid md:grid-cols-2 gap-4">
                <div>
                    <p class="text-sm text-gray-600">Customer</p>
                    <p class="font-medium">{{ $order->customer_name }}</p>
                </div>
                <div>
                    <p class="text-sm text-gray-600">Order Date</p>
                    <p class="font-medium">{{ $order->created_at->format('M j, Y \a\t g:i A') }}</p>
                </div>
                <div>
                    <p class="text-sm text-gray-600">Order Type</p>
                    <p class="font-medium">{{ ucfirst($order->order_type) }}</p>
                </div>
                <div>
                    <p class="text-sm text-gray-600">Total Amount</p>
                    <p class="font-medium text-orange-600">${{ number_format($order->total_amount, 2) }}</p>
                </div>
            </div>
        </div>

        <!-- Order Status Timeline -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-6">Order Status</h2>
            
            <div class="relative">
                <!-- Timeline Line -->
                <div class="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>
                
                <!-- Status Steps -->
                <div class="space-y-6">
                    @php
                        $statuses = [
                            'pending' => ['label' => 'Order Placed', 'icon' => 'fas fa-receipt', 'time' => $order->created_at],
                            'confirmed' => ['label' => 'Order Confirmed', 'icon' => 'fas fa-check-circle', 'time' => $order->confirmed_at],
                            'preparing' => ['label' => 'Preparing Food', 'icon' => 'fas fa-utensils', 'time' => $order->prepared_at],
                            'ready' => ['label' => 'Ready for ' . ($order->order_type === 'delivery' ? 'Delivery' : 'Pickup'), 'icon' => 'fas fa-clock', 'time' => null],
                            'out_for_delivery' => ['label' => 'Out for Delivery', 'icon' => 'fas fa-truck', 'time' => null],
                            'delivered' => ['label' => $order->order_type === 'delivery' ? 'Delivered' : 'Picked Up', 'icon' => 'fas fa-check-double', 'time' => $order->delivered_at]
                        ];
                        
                        $currentStatusIndex = array_search($order->status, array_keys($statuses));
                        $isDelivery = $order->order_type === 'delivery';
                        
                        // Remove out_for_delivery step for pickup orders
                        if (!$isDelivery) {
                            unset($statuses['out_for_delivery']);
                        }
                    @endphp
                    
                    @foreach($statuses as $statusKey => $statusInfo)
                        @php
                            $statusIndex = array_search($statusKey, array_keys($statuses));
                            $isCompleted = $statusIndex <= $currentStatusIndex;
                            $isCurrent = $statusKey === $order->status;
                            $isPending = $statusIndex > $currentStatusIndex;
                        @endphp
                        
                        <div class="relative flex items-center">
                            <!-- Status Icon -->
                            <div class="relative z-10 flex items-center justify-center w-8 h-8 rounded-full border-2
                                @if($isCompleted) bg-green-500 border-green-500 text-white
                                @elseif($isCurrent) bg-orange-500 border-orange-500 text-white
                                @else bg-white border-gray-300 text-gray-400
                                @endif">
                                <i class="{{ $statusInfo['icon'] }} text-sm"></i>
                            </div>
                            
                            <!-- Status Content -->
                            <div class="ml-4 flex-1">
                                <div class="flex items-center justify-between">
                                    <h3 class="font-medium
                                        @if($isCompleted) text-green-700
                                        @elseif($isCurrent) text-orange-700
                                        @else text-gray-500
                                        @endif">
                                        {{ $statusInfo['label'] }}
                                    </h3>
                                    
                                    @if($statusInfo['time'])
                                        <span class="text-sm text-gray-500">
                                            {{ $statusInfo['time']->format('M j, g:i A') }}
                                        </span>
                                    @elseif($isCurrent)
                                        <span class="text-sm text-orange-600 font-medium">In Progress</span>
                                    @endif
                                </div>
                                
                                @if($isCurrent)
                                    <p class="text-sm text-gray-600 mt-1">
                                        @if($statusKey === 'pending')
                                            We've received your order and are reviewing it.
                                        @elseif($statusKey === 'confirmed')
                                            Your order has been confirmed and we're starting preparation.
                                        @elseif($statusKey === 'preparing')
                                            Our chefs are preparing your delicious food.
                                        @elseif($statusKey === 'ready')
                                            Your order is ready for {{ $order->order_type === 'delivery' ? 'delivery' : 'pickup' }}.
                                        @elseif($statusKey === 'out_for_delivery')
                                            Your order is on its way to you.
                                        @elseif($statusKey === 'delivered')
                                            Your order has been {{ $order->order_type === 'delivery' ? 'delivered' : 'picked up' }}.
                                        @endif
                                    </p>
                                @endif
                            </div>
                        </div>
                    @endforeach
                    
                    @if($order->status === 'cancelled')
                        <div class="relative flex items-center">
                            <div class="relative z-10 flex items-center justify-center w-8 h-8 rounded-full border-2 bg-red-500 border-red-500 text-white">
                                <i class="fas fa-times text-sm"></i>
                            </div>
                            <div class="ml-4 flex-1">
                                <div class="flex items-center justify-between">
                                    <h3 class="font-medium text-red-700">Order Cancelled</h3>
                                    @if($order->cancelled_at)
                                        <span class="text-sm text-gray-500">
                                            {{ $order->cancelled_at->format('M j, g:i A') }}
                                        </span>
                                    @endif
                                </div>
                                @if($order->cancellation_reason)
                                    <p class="text-sm text-red-600 mt-1">{{ $order->cancellation_reason }}</p>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Estimated Time -->
        @if($order->estimated_delivery_time && !in_array($order->status, ['delivered', 'cancelled']))
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div class="flex items-center">
                    <i class="fas fa-clock text-blue-600 mr-3"></i>
                    <div>
                        <h3 class="font-medium text-blue-800">
                            Estimated {{ $order->order_type === 'delivery' ? 'Delivery' : 'Pickup' }} Time
                        </h3>
                        <p class="text-blue-700">{{ $order->estimated_delivery_time->format('M j, Y \a\t g:i A') }}</p>
                    </div>
                </div>
            </div>
        @endif

        <!-- Order Items Summary -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Order Items</h2>
            <div class="space-y-3">
                @foreach($order->orderItems as $orderItem)
                    <div class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                        <div class="flex items-center space-x-3">
                            @if($orderItem->item_image)
                                <img src="{{ $orderItem->item_image }}" alt="{{ $orderItem->item_name }}" 
                                     class="w-12 h-12 rounded-lg object-cover">
                            @else
                                <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-image text-gray-400"></i>
                                </div>
                            @endif
                            <div>
                                <h4 class="font-medium">{{ $orderItem->item_name }}</h4>
                                <p class="text-sm text-gray-600">Qty: {{ $orderItem->quantity }}</p>
                            </div>
                        </div>
                        <span class="font-semibold">${{ number_format($orderItem->total_price, 2) }}</span>
                    </div>
                @endforeach
            </div>
            
            <div class="mt-4 pt-4 border-t border-gray-200">
                <div class="flex justify-between font-bold text-lg">
                    <span>Total</span>
                    <span class="text-orange-600">${{ number_format($order->total_amount, 2) }}</span>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        @if($order->order_type === 'delivery' && $order->delivery_address)
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">Delivery Address</h2>
                <div class="text-gray-700">
                    <p>{{ $order->delivery_address }}</p>
                    <p>{{ $order->delivery_city }}, {{ $order->delivery_postal_code }}</p>
                    @if($order->delivery_instructions)
                        <p class="mt-2 text-sm text-orange-600">
                            <i class="fas fa-sticky-note mr-1"></i>
                            <strong>Instructions:</strong> {{ $order->delivery_instructions }}
                        </p>
                    @endif
                </div>
            </div>
        @endif

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            @if(!in_array($order->status, ['delivered', 'cancelled']))
                <button onclick="location.reload()" 
                        class="bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-orange-700 transition-colors">
                    <i class="fas fa-sync-alt mr-2"></i>Refresh Status
                </button>
            @endif
            
            <a href="{{ route('menu.index') }}" 
               class="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-200 transition-colors text-center">
                Order Again
            </a>
            
            <a href="{{ route('home') }}" 
               class="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-200 transition-colors text-center">
                Back to Home
            </a>
        </div>
    </div>
</div>

<script>
// Auto-refresh the page every 30 seconds if order is not completed
@if(!in_array($order->status, ['delivered', 'cancelled']))
    setTimeout(function() {
        location.reload();
    }, 30000);
@endif
</script>
@endsection
