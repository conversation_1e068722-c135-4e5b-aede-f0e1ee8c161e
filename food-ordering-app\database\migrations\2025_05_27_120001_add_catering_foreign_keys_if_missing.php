<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add foreign key constraints for catering_pricing_tiers
        if (Schema::hasTable('catering_pricing_tiers') && Schema::hasTable('catering_packages')) {
            Schema::table('catering_pricing_tiers', function (Blueprint $table) {
                if (!$this->foreignKeyExists('catering_pricing_tiers', 'catering_pricing_tiers_catering_package_id_foreign')) {
                    $table->foreign('catering_package_id')->references('id')->on('catering_packages')->onDelete('cascade');
                }
            });
        }

        // Add foreign key constraints for catering_orders
        if (Schema::hasTable('catering_orders')) {
            Schema::table('catering_orders', function (Blueprint $table) {
                if (Schema::hasTable('users') && !$this->foreignKeyExists('catering_orders', 'catering_orders_user_id_foreign')) {
                    $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
                }
                if (Schema::hasTable('catering_event_types') && !$this->foreignKeyExists('catering_orders', 'catering_orders_catering_event_type_id_foreign')) {
                    $table->foreign('catering_event_type_id')->references('id')->on('catering_event_types')->onDelete('restrict');
                }
                if (Schema::hasTable('catering_packages') && !$this->foreignKeyExists('catering_orders', 'catering_orders_catering_package_id_foreign')) {
                    $table->foreign('catering_package_id')->references('id')->on('catering_packages')->onDelete('restrict');
                }
            });
        }

        // Add foreign key constraints for catering_order_items
        if (Schema::hasTable('catering_order_items') && Schema::hasTable('catering_orders')) {
            Schema::table('catering_order_items', function (Blueprint $table) {
                if (!$this->foreignKeyExists('catering_order_items', 'catering_order_items_catering_order_id_foreign')) {
                    $table->foreign('catering_order_id')->references('id')->on('catering_orders')->onDelete('cascade');
                }
            });
        }
    }

    /**
     * Check if a foreign key exists
     */
    private function foreignKeyExists(string $table, string $foreignKeyName): bool
    {
        try {
            $connection = Schema::getConnection();
            $schemaBuilder = $connection->getSchemaBuilder();
            
            // Get all foreign keys for the table
            $foreignKeys = $connection->select("
                SELECT CONSTRAINT_NAME 
                FROM information_schema.TABLE_CONSTRAINTS 
                WHERE TABLE_SCHEMA = ? 
                AND TABLE_NAME = ? 
                AND CONSTRAINT_TYPE = 'FOREIGN KEY'
            ", [$connection->getDatabaseName(), $table]);

            foreach ($foreignKeys as $fk) {
                if ($fk->CONSTRAINT_NAME === $foreignKeyName) {
                    return true;
                }
            }
            
            return false;
        } catch (Exception $e) {
            // If we can't check, assume it doesn't exist
            return false;
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop foreign key constraints for catering_order_items
        if (Schema::hasTable('catering_order_items')) {
            Schema::table('catering_order_items', function (Blueprint $table) {
                try {
                    $table->dropForeign(['catering_order_id']);
                } catch (Exception $e) {
                    // Foreign key might not exist
                }
            });
        }

        // Drop foreign key constraints for catering_orders
        if (Schema::hasTable('catering_orders')) {
            Schema::table('catering_orders', function (Blueprint $table) {
                try {
                    $table->dropForeign(['user_id']);
                    $table->dropForeign(['catering_event_type_id']);
                    $table->dropForeign(['catering_package_id']);
                } catch (Exception $e) {
                    // Foreign keys might not exist
                }
            });
        }

        // Drop foreign key constraints for catering_pricing_tiers
        if (Schema::hasTable('catering_pricing_tiers')) {
            Schema::table('catering_pricing_tiers', function (Blueprint $table) {
                try {
                    $table->dropForeign(['catering_package_id']);
                } catch (Exception $e) {
                    // Foreign key might not exist
                }
            });
        }
    }
};
