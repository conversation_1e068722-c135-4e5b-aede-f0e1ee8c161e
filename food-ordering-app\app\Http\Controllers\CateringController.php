<?php

namespace App\Http\Controllers;

use App\Models\CateringEventType;
use App\Models\CateringPackage;
use App\Models\CateringOrder;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CateringController extends Controller
{
    /**
     * Display the catering landing page.
     */
    public function index()
    {
        $eventTypes = CateringEventType::active()->ordered()->get();
        $popularPackages = CateringPackage::active()->popular()->ordered()->take(3)->get();
        
        return view('catering.index', compact('eventTypes', 'popularPackages'));
    }

    /**
     * Show the catering booking form.
     */
    public function booking(Request $request)
    {
        $eventTypes = CateringEventType::active()->ordered()->get();
        $packages = CateringPackage::active()->ordered()->get();
        
        // Pre-fill form if parameters are provided
        $selectedEventType = $request->get('event_type');
        $selectedPackage = $request->get('package');
        $guestCount = $request->get('guests');
        
        return view('catering.booking', compact(
            'eventTypes', 
            'packages', 
            'selectedEventType', 
            'selectedPackage', 
            'guestCount'
        ));
    }

    /**
     * Get packages available for a specific event type and guest count.
     */
    public function getAvailablePackages(Request $request): JsonResponse
    {
        $eventTypeId = $request->get('event_type_id');
        $guestCount = (int) $request->get('guest_count', 0);

        $packages = CateringPackage::active()
            ->where(function ($query) use ($guestCount) {
                if ($guestCount > 0) {
                    $query->where('min_guests', '<=', $guestCount)
                          ->where('max_guests', '>=', $guestCount);
                }
            })
            ->ordered()
            ->get();

        return response()->json([
            'packages' => $packages->map(function ($package) use ($guestCount) {
                $pricing = $guestCount > 0 ? $package->calculateTotalPrice($guestCount) : null;
                
                return [
                    'id' => $package->id,
                    'name' => $package->name,
                    'slug' => $package->slug,
                    'short_description' => $package->short_description,
                    'image_url' => $package->image_url,
                    'base_price_per_person' => $package->base_price_per_person,
                    'min_guests' => $package->min_guests,
                    'max_guests' => $package->max_guests,
                    'dietary_options' => $package->dietary_options,
                    'service_includes' => $package->service_includes,
                    'is_popular' => $package->is_popular,
                    'pricing' => $pricing,
                ];
            })
        ]);
    }

    /**
     * Calculate pricing for a specific package and guest count.
     */
    public function calculatePricing(Request $request): JsonResponse
    {
        $request->validate([
            'package_id' => 'required|exists:catering_packages,id',
            'guest_count' => 'required|integer|min:1',
            'serving_staff_count' => 'nullable|integer|min:0',
            'needs_setup_service' => 'boolean',
            'needs_cleanup_service' => 'boolean',
        ]);

        $package = CateringPackage::findOrFail($request->package_id);
        $guestCount = $request->guest_count;
        
        $options = [
            'serving_staff_count' => $request->get('serving_staff_count', 0),
            'needs_setup_service' => $request->boolean('needs_setup_service'),
            'needs_cleanup_service' => $request->boolean('needs_cleanup_service'),
        ];

        if (!$package->isAvailableForGuests($guestCount)) {
            return response()->json([
                'error' => "This package is only available for {$package->min_guests}-{$package->max_guests} guests."
            ], 400);
        }

        $pricing = $package->calculateTotalPrice($guestCount, $options);

        return response()->json([
            'success' => true,
            'pricing' => $pricing,
            'package' => [
                'name' => $package->name,
                'preparation_time_hours' => $package->preparation_time_hours,
            ]
        ]);
    }

    /**
     * Check availability for a specific date and time.
     */
    public function checkAvailability(Request $request): JsonResponse
    {
        $request->validate([
            'event_date' => 'required|date|after:today',
            'event_time' => 'required|date_format:H:i',
            'event_type_id' => 'required|exists:catering_event_types,id',
        ]);

        $eventType = CateringEventType::findOrFail($request->event_type_id);
        $eventDateTime = \Carbon\Carbon::createFromFormat(
            'Y-m-d H:i', 
            $request->event_date . ' ' . $request->event_time
        );

        // Check if the date/time meets minimum advance booking requirements
        if (!$eventType->isDateTimeAvailable($eventDateTime)) {
            $minTime = $eventType->getMinimumBookingTime();
            return response()->json([
                'available' => false,
                'message' => "This event type requires at least {$eventType->advance_booking_hours} hours advance booking. Earliest available time: " . $minTime->format('M j, Y g:i A')
            ]);
        }

        // Check for existing bookings (simplified - you might want more complex logic)
        $existingOrders = CateringOrder::where('event_date', $request->event_date)
            ->where('event_start_time', $request->event_time)
            ->whereNotIn('status', ['cancelled'])
            ->count();

        // Assume we can handle up to 5 events at the same time (adjust as needed)
        $maxConcurrentEvents = 5;
        $available = $existingOrders < $maxConcurrentEvents;

        return response()->json([
            'available' => $available,
            'message' => $available ? 'Time slot is available' : 'This time slot is fully booked. Please choose a different time.',
            'existing_bookings' => $existingOrders,
        ]);
    }

    /**
     * Show package details.
     */
    public function showPackage(CateringPackage $package)
    {
        $package->load('pricingTiers');
        
        return view('catering.package', compact('package'));
    }
}
