<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->onDelete('cascade');

            // Item details - either food item or package
            $table->foreignId('food_item_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('package_id')->nullable()->constrained()->onDelete('cascade');

            // Item information at time of order (for historical accuracy)
            $table->string('item_name');
            $table->text('item_description')->nullable();
            $table->string('item_image')->nullable();

            // Quantity and pricing
            $table->integer('quantity');
            $table->decimal('unit_weight', 8, 3)->nullable(); // For bulk orders
            $table->decimal('unit_price', 10, 2); // Price at time of order
            $table->decimal('total_price', 10, 2);

            // Order type
            $table->enum('order_type', ['unit', 'bulk'])->default('unit');

            // Customizations
            $table->json('customizations')->nullable();
            $table->text('special_instructions')->nullable();

            // Item status (for individual item tracking)
            $table->enum('status', [
                'pending',
                'confirmed',
                'preparing',
                'ready',
                'served'
            ])->default('pending');

            $table->timestamps();

            // Indexes
            $table->index(['order_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
};
