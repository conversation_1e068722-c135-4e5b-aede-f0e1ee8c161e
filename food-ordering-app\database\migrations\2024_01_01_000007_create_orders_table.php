<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number')->unique();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            
            // Customer Information (for guest orders)
            $table->string('customer_name');
            $table->string('customer_email');
            $table->string('customer_phone');
            
            // Delivery Information
            $table->text('delivery_address');
            $table->string('delivery_city');
            $table->string('delivery_postal_code');
            $table->text('delivery_instructions')->nullable();
            
            // Order Details
            $table->enum('order_type', ['delivery', 'pickup'])->default('delivery');
            $table->enum('payment_method', ['cash', 'card', 'online', 'wallet'])->default('cash');
            $table->enum('payment_status', ['pending', 'paid', 'failed', 'refunded'])->default('pending');
            
            // Pricing
            $table->decimal('subtotal', 10, 2);
            $table->decimal('tax_amount', 10, 2)->default(0);
            $table->decimal('delivery_fee', 10, 2)->default(0);
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->decimal('total_amount', 10, 2);
            
            // Status and Timing
            $table->enum('status', [
                'pending', 
                'confirmed', 
                'preparing', 
                'ready', 
                'out_for_delivery', 
                'delivered', 
                'cancelled'
            ])->default('pending');
            
            $table->datetime('estimated_delivery_time')->nullable();
            $table->datetime('confirmed_at')->nullable();
            $table->datetime('prepared_at')->nullable();
            $table->datetime('delivered_at')->nullable();
            $table->datetime('cancelled_at')->nullable();
            
            // Additional Information
            $table->text('notes')->nullable();
            $table->text('cancellation_reason')->nullable();
            $table->json('status_history')->nullable(); // Track status changes
            
            $table->timestamps();
            
            // Indexes
            $table->index(['status', 'created_at']);
            $table->index(['customer_phone']);
            $table->index(['order_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
