# Admin Routes Documentation

## Route Structure Overview

All admin routes are properly separated from normal user routes using the `/admin` prefix and protected by authentication and admin middleware.

## Admin Route Configuration

```php
Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
    // All admin routes here
});
```

### Middleware Protection
- **auth**: Ensures user is logged in
- **admin**: Ensures user has admin role (admin or super_admin)

## Complete Admin Routes List

### Admin Authentication
- **GET** `/admin/login` → `admin.login`
  - Show admin login form (separate from customer login)
- **POST** `/admin/login` → Admin login processing
  - Validates admin credentials and role
- **POST** `/admin/logout` → `admin.logout`
  - Admin logout (redirects to admin login)

### Dashboard
- **GET** `/admin` → `admin.dashboard`
  - Admin dashboard with statistics and overview

### User Management
- **GET** `/admin/users` → `admin.users.index`
  - List all users with search and filters
- **GET** `/admin/users/create` → `admin.users.create`
  - Show create user form
- **POST** `/admin/users` → `admin.users.store`
  - Store new user
- **GET** `/admin/users/{user}` → `admin.users.show`
  - Show user details
- **GET** `/admin/users/{user}/edit` → `admin.users.edit`
  - Show edit user form
- **PUT/PATCH** `/admin/users/{user}` → `admin.users.update`
  - Update user
- **DELETE** `/admin/users/{user}` → `admin.users.destroy`
  - Delete user
- **PATCH** `/admin/users/{user}/toggle-status` → `admin.users.toggle-status`
  - Toggle user active status

### Food Item Management
- **GET** `/admin/food-items` → `admin.food-items.index`
  - List all food items with search and filters
- **GET** `/admin/food-items/create` → `admin.food-items.create`
  - Show create food item form
- **POST** `/admin/food-items` → `admin.food-items.store`
  - Store new food item
- **GET** `/admin/food-items/{foodItem}` → `admin.food-items.show`
  - Show food item details
- **GET** `/admin/food-items/{foodItem}/edit` → `admin.food-items.edit`
  - Show edit food item form
- **PUT/PATCH** `/admin/food-items/{foodItem}` → `admin.food-items.update`
  - Update food item
- **DELETE** `/admin/food-items/{foodItem}` → `admin.food-items.destroy`
  - Delete food item
- **PATCH** `/admin/food-items/{foodItem}/toggle-availability` → `admin.food-items.toggle-availability`
  - Toggle food item availability
- **POST** `/admin/food-items/bulk-action` → `admin.food-items.bulk-action`
  - Perform bulk actions on food items

### Order Management
- **GET** `/admin/orders` → `admin.orders.index`
  - List all orders with search and filters
- **GET** `/admin/orders/{order}` → `admin.orders.show`
  - Show order details
- **PATCH** `/admin/orders/{order}/status` → `admin.orders.update-status`
  - Update order status
- **PATCH** `/admin/orders/{order}/payment-status` → `admin.orders.update-payment-status`
  - Update payment status
- **GET** `/admin/orders/{order}/invoice` → `admin.orders.invoice`
  - Print order invoice
- **GET** `/admin/orders-export` → `admin.orders.export`
  - Export orders to CSV
- **GET** `/admin/orders-statistics` → `admin.orders.statistics`
  - View order statistics

### Category Management
- **GET** `/admin/categories` → `admin.categories.index`
  - List all categories
- **GET** `/admin/categories/create` → `admin.categories.create`
  - Show create category form
- **POST** `/admin/categories` → `admin.categories.store`
  - Store new category
- **GET** `/admin/categories/{category}` → `admin.categories.show`
  - Show category details
- **GET** `/admin/categories/{category}/edit` → `admin.categories.edit`
  - Show edit category form
- **PUT/PATCH** `/admin/categories/{category}` → `admin.categories.update`
  - Update category
- **DELETE** `/admin/categories/{category}` → `admin.categories.destroy`
  - Delete category
- **PATCH** `/admin/categories/{category}/toggle-status` → `admin.categories.toggle-status`
  - Toggle category status
- **POST** `/admin/categories/bulk-action` → `admin.categories.bulk-action`
  - Perform bulk actions on categories

### Cuisine Management
- **GET** `/admin/cuisines` → `admin.cuisines.index`
  - List all cuisines
- **GET** `/admin/cuisines/create` → `admin.cuisines.create`
  - Show create cuisine form
- **POST** `/admin/cuisines` → `admin.cuisines.store`
  - Store new cuisine
- **GET** `/admin/cuisines/{cuisine}` → `admin.cuisines.show`
  - Show cuisine details
- **GET** `/admin/cuisines/{cuisine}/edit` → `admin.cuisines.edit`
  - Show edit cuisine form
- **PUT/PATCH** `/admin/cuisines/{cuisine}` → `admin.cuisines.update`
  - Update cuisine
- **DELETE** `/admin/cuisines/{cuisine}` → `admin.cuisines.destroy`
  - Delete cuisine
- **PATCH** `/admin/cuisines/{cuisine}/toggle-status` → `admin.cuisines.toggle-status`
  - Toggle cuisine status
- **POST** `/admin/cuisines/bulk-action` → `admin.cuisines.bulk-action`
  - Perform bulk actions on cuisines

## Normal User Routes (Non-Admin)

These routes are accessible to all users and do NOT have the `/admin` prefix:

### Public Routes
- **GET** `/` → `home`
- **GET** `/search` → `search`
- **GET** `/login` → `login` (Customer login only)
- **POST** `/login` → Customer login processing
- **POST** `/logout` → `logout` (General logout)

### Menu Routes
- **GET** `/menu` → `menu.index`
- **GET** `/menu/food-item/{foodItem:slug}` → `menu.food-item`
- **GET** `/menu/package/{package:slug}` → `menu.package`
- **GET** `/menu/category/{category:slug}` → `menu.category`
- **GET** `/menu/cuisine/{cuisine:slug}` → `menu.cuisine`

### Cart Routes
- **GET** `/cart` → `cart.index`
- **POST** `/cart/add` → `cart.add`
- **PATCH** `/cart/update/{cartItem}` → `cart.update`
- **DELETE** `/cart/remove/{cartItem}` → `cart.remove`
- **DELETE** `/cart/clear` → `cart.clear`
- **GET** `/cart/count` → `cart.count`

### Order Routes
- **GET** `/orders/checkout` → `orders.checkout`
- **POST** `/orders/place` → `orders.place`
- **GET** `/orders/confirmation/{order:order_number}` → `orders.confirmation`
- **GET** `/orders/track/{order:order_number}` → `orders.track`

### Catering Routes
- **GET** `/catering` → `catering.index`
- **GET** `/catering/booking` → `catering.booking`
- **GET** `/catering/packages/{package:slug}` → `catering.package`
- **GET** `/catering/api/packages` → `catering.api.packages`
- **POST** `/catering/api/pricing` → `catering.api.pricing`
- **POST** `/catering/api/availability` → `catering.api.availability`
- **POST** `/catering/order` → `catering.order.store`
- **GET** `/catering/confirmation/{cateringOrder:order_number}` → `catering.confirmation`
- **GET** `/catering/track/{cateringOrder:order_number}` → `catering.track`

## Security Features

### Route Protection
1. **Admin Middleware**: All `/admin/*` routes require admin role
2. **Authentication**: Admin routes require user to be logged in
3. **Role Verification**: Checks user role on every admin request
4. **CSRF Protection**: All forms include CSRF tokens

### Access Control
- **Super Admin**: Full access to all admin features
- **Admin**: Limited access (cannot delete super admins)
- **Customer**: No admin access, redirected with 403 error

### URL Structure Benefits
1. **Clear Separation**: Admin and user routes are clearly separated
2. **SEO Friendly**: User-facing routes are clean and descriptive
3. **Security**: Admin routes are easily identifiable and protectable
4. **Maintenance**: Easy to manage and update admin functionality

## Testing Admin Routes

To test admin access:
1. Navigate to `/admin/login` (dedicated admin login)
2. Login with admin credentials
3. Navigate to `/admin` to access dashboard
4. All admin functionality is under `/admin/*` URLs
5. Non-admin users get 403 error when accessing admin routes

### Login Separation Testing
- **Customer Login:** `/login` → Only allows customer access
- **Admin Login:** `/admin/login` → Validates admin role and active status
- **Cross-access:** Customers cannot access admin routes, admins can access customer features

## Route Naming Convention

All admin routes follow the pattern: `admin.{resource}.{action}`
- `admin.dashboard` - Admin dashboard
- `admin.users.index` - User listing
- `admin.food-items.create` - Create food item
- `admin.orders.show` - Show order details

This ensures consistency and makes route generation predictable throughout the application.
